{"name": "聪学伴学", "version": "5.2.0", "description": "聪学AI", "author": "LionLi", "license": "MIT", "type": "module", "scripts": {"dev": "vite serve --mode development", "build:prod": "vite build --mode production", "build:dev": "vite build --mode development", "preview": "vite preview", "lint:eslint": "eslint  --fix --ext .ts,.js,.vue ./src ", "prettier": "prettier --write ."}, "repository": {"type": "git", "url": "https://gitee.com/JavaLionLi/plus-ui.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@highlightjs/vue-plugin": "2.1.0", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.9.0", "animate.css": "4.1.1", "await-to-js": "3.0.0", "axios": "1.6.8", "bpmn-js": "16.4.0", "crypto-js": "4.2.0", "diagram-js": "12.3.0", "didi": "9.0.2", "echarts": "^5.5.0", "element-plus": "2.7.5", "file-saver": "2.0.5", "fuse.js": "7.0.0", "highlight.js": "11.9.0", "image-conversion": "^2.1.1", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "katex": "^0.16.22", "nprogress": "0.2.0", "pinia": "2.1.7", "qrcode": "^1.5.4", "screenfull": "6.0.2", "vue": "3.4.25", "vue-cropper": "1.1.1", "vue-i18n": "9.10.2", "vue-router": "4.3.2", "vue-types": "5.1.1", "vuedraggable": "^4.1.0", "vxe-table": "4.5.22"}, "devDependencies": {"@iconify/json": "2.2.201", "@intlify/unplugin-vue-i18n": "3.0.1", "@types/crypto-js": "4.2.2", "@types/file-saver": "2.0.7", "@types/js-cookie": "3.0.6", "@types/node": "18.18.2", "@types/nprogress": "0.2.3", "@typescript-eslint/eslint-plugin": "7.3.1", "@typescript-eslint/parser": "7.3.1", "@unocss/preset-attributify": "0.58.6", "@unocss/preset-icons": "0.58.6", "@unocss/preset-uno": "0.58.6", "@vitejs/plugin-vue": "5.0.4", "@vue/compiler-sfc": "3.4.23", "autoprefixer": "10.4.18", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-define-config": "2.1.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-promise": "6.1.1", "eslint-plugin-vue": "9.23.0", "fast-glob": "3.3.2", "postcss": "8.4.36", "prettier": "3.2.5", "sass": "1.72.0", "typescript": "5.4.5", "unocss": "0.58.6", "unplugin-auto-import": "0.17.5", "unplugin-icons": "0.18.5", "unplugin-vue-components": "0.26.0", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "5.2.10", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1", "vitest": "1.5.0", "vue-eslint-parser": "9.4.2", "vue-tsc": "2.0.13"}}