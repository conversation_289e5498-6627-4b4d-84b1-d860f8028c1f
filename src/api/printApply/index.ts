import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { PrintApplyVO, PrintApplyForm, PrintApplyQuery } from '@/api/printApply/types';

/**
 * 查询打印申请列表
 * @param query
 * @returns {*}
 */

export const listPrintApply = (query?: PrintApplyQuery): AxiosPromise<PrintApplyVO[]> => {
  return request({
    url: '/printApply/printApply/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询打印申请详细
 * @param applyId
 */
export const getPrintApply = (applyId: string | number): AxiosPromise<PrintApplyVO> => {
  return request({
    url: '/printApply/printApply/' + applyId,
    method: 'get'
  });
};

/**
 * 新增打印申请
 * @param data
 */
export const addPrintApply = (data: PrintApplyForm) => {
  return request({
    url: '/printApply/printApply',
    method: 'post',
    data: data
  });
};

/**
 * 修改打印申请
 * @param data
 */
export const updatePrintApply = (data: PrintApplyForm) => {
  return request({
    url: '/printApply/printApply',
    method: 'put',
    data: data
  });
};

/**
 * 修改打印状态
 * @param data
 */
export const updatePrintStatus = (data: PrintApplyForm) => {
  return request({
    url: '/printApply/printApply/print',
    method: 'put',
    data: data
  });
};
/**
 * 删除打印申请
 * @param applyId
 */
export const delPrintApply = (applyId: string | number | Array<string | number>) => {
  return request({
    url: '/printApply/printApply/' + applyId,
    method: 'delete'
  });
};
