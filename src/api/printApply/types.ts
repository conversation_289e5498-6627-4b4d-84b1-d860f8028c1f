export interface PrintApplyVO {
  /**
   * 主键ID
   */
  applyId: string | number;

  /**
   * 课程ID
   */
  courseId: string | number;

  /**
   * 视频资源ID
   */
  resourceId: string | number;

  /**
   * 知识点
   */
  pointIds: string | number;

  /**
   * 会员id
   */
  userId: string | number;

  /**
   * 会员名
   */
  userName: string;

  /**
   * 删除标志（0代表未打印 1已打印）
   */
  printStatus: string;

  /**
   * 试题编号
   */
  examNo: string;

  /**
   * 批改后的url图片地址
   */
  correctImageUrl: string;

  /**
   * jyeoo组卷名称
   */
  jyeooTitle: string;

  /**
   * jyeoo难度
   */
  degree: number;

  /**
   * jyeoo创建者
   */
  jyeooCreatorId: string | number;

  /**
   * jyeoo组卷标识
   */
  jyeooId: string | number;
  

  /**
   * 昵称
   */
  nickName: string;

  /**
   * 打印的pdf地址
   */
  pdfUrl: string;
  
}

export interface PrintApplyForm extends BaseEntity {
  /**
   * 主键ID
   */
  applyId?: string | number;

  /**
   * 课程ID
   */
  courseId?: string | number;

  /**
   * 视频资源ID
   */
  resourceId?: string | number;

  /**
   * 知识点
   */
  pointIds?: string | number;

  /**
   * 会员id
   */
  userId?: string | number;

  /**
   * 会员名
   */
  userName?: string;

  /**
   * 删除标志（0代表未打印 1已打印）
   */
  printStatus?: string;

  /**
   * 试题编号
   */
  examNo?: string;

  /**
   * 批改后的url图片地址
   */
  correctImageUrl?: string;


  /**
   * jyeoo组卷名称
   */
  jyeooTitle?: string;

  /**
   * jyeoo难度
   */
  degree?: number;

  /**
   * jyeoo创建者
   */
  jyeooCreatorId?: string | number;

  /**
   * jyeoo组卷标识
   */
  jyeooId?: string | number;

  /**
   * 昵称
   */
  nickName: string;

}

export interface PrintApplyQuery extends PageQuery {

  /**
   * 会员名
   */
  userName?: string;

  /**
   * 昵称
   */
  nickName: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



