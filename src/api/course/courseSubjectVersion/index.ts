import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CourseSubjectVersionVO, CourseSubjectVersionForm, CourseSubjectVersionQuery } from '@/api/course/courseSubjectVersion/types';
import { CourseSubjectVO} from '@/api/courseSubject/types';

/**
 * 查询课程版本列表
 * @param query
 * @returns {*}
 */

export const listCourseSubjectVersion = (query?: CourseSubjectVersionQuery): AxiosPromise<CourseSubjectVersionVO[]> => {
  return request({
    url: '/course/courseSubjectVersion/list',
    method: 'get',
    params: query
  });
};

export const listSubject = (query?: CourseSubjectVersionQuery): AxiosPromise<CourseSubjectVO[]> => {
  return request({
    url: '/course/courseSubjectVersion/listSubject',
    method: 'get',
    params: query
  });
};
export const listVersion = (query?: CourseSubjectVersionQuery): AxiosPromise<CourseSubjectVersionVO[]> => {
  return request({
    url: '/course/courseSubjectVersion/listVersion',
    method: 'get',
    params: query
  });
};

/**
 * 查询课程版本详细
 * @param versionId
 */
export const getCourseSubjectVersion = (versionId: string | number): AxiosPromise<CourseSubjectVersionVO> => {
  return request({
    url: '/course/courseSubjectVersion/' + versionId,
    method: 'get'
  });
};

/**
 * 新增课程版本
 * @param data
 */
export const addCourseSubjectVersion = (data: CourseSubjectVersionForm) => {
  return request({
    url: '/course/courseSubjectVersion',
    method: 'post',
    data: data
  });
};

/**
 * 修改课程版本
 * @param data
 */
export const updateCourseSubjectVersion = (data: CourseSubjectVersionForm) => {
  return request({
    url: '/course/courseSubjectVersion',
    method: 'put',
    data: data
  });
};

/**
 * 删除课程版本
 * @param versionId
 */
export const delCourseSubjectVersion = (versionId: string | number | Array<string | number>) => {
  return request({
    url: '/course/courseSubjectVersion/' + versionId,
    method: 'delete'
  });
};
