export interface CourseSubjectVersionVO {
  /**
   * 主键ID
   */
  versionId: string | number;

  /**
   * 课程ID
   */
  courseId: string | number;

  /**
   * 科目ID
   */
  subjectId: string | number;

  /**
   * 版本名称
   */
  versionName: string;


  /**
   * 科目ID
   */
  subjectName: string;

}

export interface CourseSubjectVersionForm extends BaseEntity {
  /**
   * 主键ID
   */
  versionId?: string | number;

  /**
   * 课程ID
   */
  courseId?: string | number;

  /**
   * 科目ID
   */
  subjectId?: string | number;

  /**
   * 版本名称
   */
  versionName?: string;

}

export interface CourseSubjectVersionQuery extends PageQuery {

  /**
   * 课程ID
   */
  courseId?: string | number;

  /**
   * 科目ID
   */
  subjectId?: string | number;

  /**
   * 版本名称
   */
  versionName?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



