import { CourseResourceVO} from '@/api/course/courseResource/types';
export interface CourseVO {
  /**
   * 主键ID
   */
  courseId: string | number;

  /**
   * 课程名称
   */
  courseName: string;

  /**
   * 副标题
   */
  subTitle: string;

  /**
   * 介绍
   */
  description: string;

  /**
   * 班级类型
   */
  typeId: string | number;

  /**
   * 推荐图URL
   */
  recommendImage: string;

  /**
   * 列表图URL
   */
  listImage: string;

  /**
   * 详情图URL
   */
  detailImage: string;

  /**
   * 是否置顶推荐: Y-是, N-否
   */
  recommendStatus: string;

  resourceVoList: CourseResourceVO[];

}

export interface CourseForm extends BaseEntity {
  /**
   * 主键ID
   */
  courseId?: string | number;

  /**
   * 课程名称
   */
  courseName?: string;

  /**
   * 副标题
   */
  subTitle?: string;

  /**
   * 介绍
   */
  description?: string;

  /**
   * 班级类型
   */
  typeId?: string | number;

  /**
   * 推荐图URL
   */
  recommendImage?: string;

  /**
   * 列表图URL
   */
  listImage?: string;

  /**
   * 详情图URL
   */
  detailImage?: string;

  /**
   * 是否置顶推荐: Y-是, N-否
   */
  recommendStatus?: string;

   /**
   * 科目ids
   */
  subjects?: number[];

}

export interface CourseQuery extends PageQuery {

  /**
   * 课程名称
   */
  courseName?: string;

  /**
   * 副标题
   */
  subTitle?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



