import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CourseVO, CourseForm, CourseQuery } from '@/api/course/course/types';

/**
 * 查询课程管理列表
 * @param query
 * @returns {*}
 */

export const listCourse = (query?: CourseQuery): AxiosPromise<CourseVO[]> => {
  return request({
    url: '/system/course/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询课程管理详细
 * @param courseId
 */
export const getCourse = (courseId: string | number): AxiosPromise<CourseVO> => {
  return request({
    url: '/system/course/' + courseId,
    method: 'get'
  });
};

/**
 * 新增课程管理
 * @param data
 */
export const addCourse = (data: CourseForm) => {
  return request({
    url: '/system/course',
    method: 'post',
    data: data
  });
};

/**
 * 修改课程管理
 * @param data
 */
export const updateCourse = (data: CourseForm) => {
  return request({
    url: '/system/course',
    method: 'put',
    data: data
  });
};

/**
 * 删除课程管理
 * @param courseId
 */
export const delCourse = (courseId: string | number | Array<string | number>) => {
  return request({
    url: '/system/course/' + courseId,
    method: 'delete'
  });
};
