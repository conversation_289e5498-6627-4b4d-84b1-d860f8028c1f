import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ClassTypeVO, ClassTypeForm, ClassTypeQuery } from '@/api/course/classType/types';

/**
 * 查询班型列表
 * @param query
 * @returns {*}
 */

export const listClassType = (query?: ClassTypeQuery): AxiosPromise<ClassTypeVO[]> => {
  return request({
    url: '/system/classType/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询班型详细
 * @param typeId
 */
export const getClassType = (typeId: string | number): AxiosPromise<ClassTypeVO> => {
  return request({
    url: '/system/classType/' + typeId,
    method: 'get'
  });
};

/**
 * 新增班型
 * @param data
 */
export const addClassType = (data: ClassTypeForm) => {
  return request({
    url: '/system/classType',
    method: 'post',
    data: data
  });
};

/**
 * 修改班型
 * @param data
 */
export const updateClassType = (data: ClassTypeForm) => {
  return request({
    url: '/system/classType',
    method: 'put',
    data: data
  });
};

/**
 * 删除班型
 * @param typeId
 */
export const delClassType = (typeId: string | number | Array<string | number>) => {
  return request({
    url: '/system/classType/' + typeId,
    method: 'delete'
  });
};
  /**
 * 班型选择
 */
  export const optionSelectClassType = (): AxiosPromise<ClassTypeVO[]> => {
    return request({
      url: '/system/classType/queryList',
      method: 'get'
    });
  };