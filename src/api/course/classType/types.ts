export interface ClassTypeVO {
  /**
   * 主键ID
   */
  typeId: string | number;

  /**
   * 班型名称
   */
  typeName: string;

}

export interface ClassTypeForm extends BaseEntity {
  /**
   * 主键ID
   */
  typeId?: string | number;

  /**
   * 班型名称
   */
  typeName?: string;

}

export interface ClassTypeQuery extends PageQuery {

  /**
   * 班型名称
   */
  typeName?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



