export interface CourseSubjectUnitVO {
  /**
   * 主键ID
   */
  unitId: string | number;

  /**
   * 课程ID
   */
  courseId: string | number;

  /**
   * 科目ID
   */
  subjectId: string | number;

  /**
   * 版本ID
   */
  versionId: string | number;

  /**
   * 单元名称
   */
  unitName: string;

  /**
   * 顺序
   */
  orderNum: number;

}

export interface CourseSubjectUnitForm extends BaseEntity {
  /**
   * 主键ID
   */
  unitId?: string | number;

  /**
   * 课程ID
   */
  courseId?: string | number;

  /**
   * 科目ID
   */
  subjectId?: string | number;

  /**
   * 版本ID
   */
  versionId?: string | number;

  /**
   * 单元名称
   */
  unitName?: string;
  /**
   * 顺序
   */
  orderNum: number;

}

export interface CourseSubjectUnitQuery extends PageQuery {

  /**
   * 课程ID
   */
  courseId?: string | number;

  /**
   * 科目ID
   */
  subjectId?: string | number;

  /**
   * 版本ID
   */
  versionId?: string | number;

  /**
   * 单元名称
   */
  unitName?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}