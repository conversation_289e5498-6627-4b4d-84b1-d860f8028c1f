import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CourseSubjectUnitVO, CourseSubjectUnitForm, CourseSubjectUnitQuery } from '@/api/course/courseSubjectUnit/types';

/**
 * 查询课程单元列表
 * @param query
 * @returns {*}
 */

export const listCourseSubjectUnit = (query?: CourseSubjectUnitQuery): AxiosPromise<CourseSubjectUnitVO[]> => {
  return request({
    url: '/course/courseSubjectUnit/list',
    method: 'get',
    params: query
  });
};

export const listUnit = (query?: CourseSubjectUnitQuery): AxiosPromise<CourseSubjectUnitVO[]> => {
  return request({
    url: '/course/courseSubjectUnit/listUnit',
    method: 'get',
    params: query
  });
};


/**
 * 查询课程单元详细
 * @param unitId
 */
export const getCourseSubjectUnit = (unitId: string | number): AxiosPromise<CourseSubjectUnitVO> => {
  return request({
    url: '/course/courseSubjectUnit/' + unitId,
    method: 'get'
  });
};

/**
 * 新增课程单元
 * @param data
 */
export const addCourseSubjectUnit = (data: CourseSubjectUnitForm) => {
  return request({
    url: '/course/courseSubjectUnit',
    method: 'post',
    data: data
  });
};

/**
 * 修改课程单元
 * @param data
 */
export const updateCourseSubjectUnit = (data: CourseSubjectUnitForm) => {
  return request({
    url: '/course/courseSubjectUnit',
    method: 'put',
    data: data
  });
};

/**
 * 删除课程单元
 * @param unitId
 */
export const delCourseSubjectUnit = (unitId: string | number | Array<string | number>) => {
  return request({
    url: '/course/courseSubjectUnit/' + unitId,
    method: 'delete'
  });
};
