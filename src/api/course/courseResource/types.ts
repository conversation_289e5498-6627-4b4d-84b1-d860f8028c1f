export interface CourseResourceVO {
  /**
   * 主键ID
   */
  resourceId: string | number;

  /**
   * 课程id
   */
  courseId: string | number;

  /**
   * 视频资源名称
   */
  videoResourceName: string | number;

  /**
   * 科目
   */
  subjectId?: string | number;

  /**
   * 版本 来源字典表 苏教版、人教版
   */
  courseVersion: string;

  /**
   * 名师
   */
  courseTeacher: string;

  /**
   * 大纲 来源于字典表
   */
  courseSyllabus: string;

  /**
   * 视频课链接
   */
  videoCourseLink: string | number;

  /**
   * 讲义链接
   */
  courseNotes: string;

  /**
   * 课后练习链接
   */
  courseExercises: string;

  /**
   * 视频时长分钟
   */
  videoMinute: string | number;

  /**
   * 视频时长秒
   */
  videoSecond: string | number;

  /**
   * 显示顺序
   */
  orderNum: number;
  /**
   * 知识点
   */
  pointId?: number;

  /**
   * 知识点回显路径
   */
  pointExpandedKeys: Array<string | number>;

  /**
   * 版本ID
   */
  versionId: string | number;

  /**
   * 名师ID
   */
  teacherId: string | number;

  /**
   * 版本名
   */
  versionName: string;

  /**
   * 科目名
   */
  subjectName: string;

  /**
   * 老师名
   */
  teacherName: string;
  /**
   * 菁优试题ids
   */
  jyeooPaperIds: string;

  /**
   * 讲义链接文件id
   */
  courseNotesFile: string;

  /**
   * 视频文件
   */
  videoCourseFile: string;
}

export interface CourseResourceForm extends BaseEntity {
  /**
   * 主键ID
   */
  resourceId?: string | number;

  /**
   * 课程id
   */
  courseId?: string | number;

  /**
   * 视频资源名称
   */
  videoResourceName?: string | number;

  /**
   * 科目
   */
  subjectId?: string | number;

  /**
   * 版本 来源字典表 苏教版、人教版
   */
  courseVersion?: string;

  /**
   * 名师
   */
  courseTeacher?: string;

  /**
   * 大纲 来源于字典表
   */
  courseSyllabus?: string;

  /**
   * 视频课链接
   */
  videoCourseLink?: string | number;

  /**
   * 讲义链接
   */
  courseNotes?: string;

  /**
   * 课后练习链接
   */
  courseExercises?: string;

  /**
   * 视频时长分钟
   */
  videoMinute?: string | number;

  /**
   * 视频时长秒
   */
  videoSecond?: string | number;

  /**
   * 显示顺序
   */
  orderNum?: number;

  /**
   * 知识点
   */
  pointIds?: string;

  /**
   * 回显
   */
  pointIdList?: number[];

  /**
   * 知识点回显路径
   */
  pointExpandedKeys: number[];

  /**
   * 版本ID
   */
  versionId: string | number;

  /**
   * 名师ID
   */
  teacherId: string | number;
  /**
   * 菁优试题ids
   */
  jyeooPaperIds: string;

  /**
   * 讲义链接文件id
   */
  courseNotesFile: string;

  /**
   * 视频文件
   */
  videoCourseFile: string;
}

export interface CourseResourceQuery extends PageQuery {

  /**
   * 视频资源名称
   */
  videoResourceName?: string | number;
  /**
 * 课程id
 */
  courseId?: string | number;

  /**
   * 日期范围参数
   */
  params?: any;
}
export interface CourseResourceQuerySchedule{
  /**
   * 班级类型
   */
  typeId: string | number;

  /**
   * 科目
   */
  subjectId?: string | number;
}


