export interface CourseSubjectTeacherVO {
  /**
   * 主键ID
   */
  teacherId: string | number;

  /**
   * 课程ID
   */
  courseId: string | number;

  /**
   * 科目ID
   */
  subjectId: string | number;

  /**
   * 版本ID
   */
  versionId: string | number;

  /**
   * 老师姓名
   */
  teacherName: string;

}

export interface CourseSubjectTeacherForm extends BaseEntity {
  /**
   * 主键ID
   */
  teacherId?: string | number;

  /**
   * 课程ID
   */
  courseId?: string | number;

  /**
   * 科目ID
   */
  subjectId?: string | number;

  /**
   * 版本ID
   */
  versionId?: string | number;

  /**
   * 老师姓名
   */
  teacherName?: string;

}

export interface CourseSubjectTeacherQuery extends PageQuery {

  /**
   * 老师姓名
   */
  teacherName?: string;

  /**
   * 课程id
   */
  courseId?: number|string;
  /**
   * 科目id
   */
  subjectId?: number|string;

  /**
   * 日期范围参数
   */
  params?: any;
}



