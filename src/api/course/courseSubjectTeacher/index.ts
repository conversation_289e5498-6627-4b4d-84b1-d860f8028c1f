import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CourseSubjectTeacherVO, CourseSubjectTeacherForm, CourseSubjectTeacherQuery } from '@/api/course/courseSubjectTeacher/types';

/**
 * 查询名师列表
 * @param query
 * @returns {*}
 */

export const listCourseSubjectTeacher = (query?: CourseSubjectTeacherQuery): AxiosPromise<CourseSubjectTeacherVO[]> => {
  return request({
    url: '/course/courseSubjectTeacher/list',
    method: 'get',
    params: query
  });
};

export const listTeacher = (query?: CourseSubjectTeacherQuery): AxiosPromise<CourseSubjectTeacherVO[]> => {
  return request({
    url: '/course/courseSubjectTeacher/listTeacher',
    method: 'get',
    params: query
  });
};


/**
 * 查询名师详细
 * @param teacherId
 */
export const getCourseSubjectTeacher = (teacherId: string | number): AxiosPromise<CourseSubjectTeacherVO> => {
  return request({
    url: '/course/courseSubjectTeacher/' + teacherId,
    method: 'get'
  });
};

/**
 * 新增名师
 * @param data
 */
export const addCourseSubjectTeacher = (data: CourseSubjectTeacherForm) => {
  return request({
    url: '/course/courseSubjectTeacher',
    method: 'post',
    data: data
  });
};

/**
 * 修改名师
 * @param data
 */
export const updateCourseSubjectTeacher = (data: CourseSubjectTeacherForm) => {
  return request({
    url: '/course/courseSubjectTeacher',
    method: 'put',
    data: data
  });
};

/**
 * 删除名师
 * @param teacherId
 */
export const delCourseSubjectTeacher = (teacherId: string | number | Array<string | number>) => {
  return request({
    url: '/course/courseSubjectTeacher/' + teacherId,
    method: 'delete'
  });
};
