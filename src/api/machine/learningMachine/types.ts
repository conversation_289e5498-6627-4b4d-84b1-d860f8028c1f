export interface LearningMachineVO {
  /**
   * 主键ID
   */
  machineId: string | number;

  /**
   * 学区ID
   */
  deptId: string | number;

  /**
   * mac地址
   */
  macAddr: string;

  /**
   * 位置状态 abnormal:异常，normal:异常
   */
  addrStatus: string;

  /**
   * 省份
   */
  provinceCode: string;
  provinceName: string;

  /**
   * 市编码
   */
  cityCode: string;

  cityName: string;

  /**
   * 区
   */
  districtCode: string;

  districtName: string;

  /**
   * 状态（0正常 1停用）
   */
  status: string;

  deptName: string;

}

export interface LearningMachineForm extends BaseEntity {
  /**
   * 主键ID
   */
  machineId?: string | number;

  /**
   * 学区ID
   */
  deptId?: string | number;

  /**
   * mac地址
   */
  macAddr?: string;

  /**
   * 位置状态 abnormal:异常，normal:异常
   */
  addrStatus?: string;

  /**
   * 省份
   */
  provinceCode?: string;

  /**
   * 市编码
   */
  cityCode?: string;

  /**
   * 区
   */
  districtCode?: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

}

export interface LearningMachineQuery extends PageQuery {

  /**
   * mac地址
   */
  macAddr?: string;

  deptId: string | number;

    /**
     * 日期范围参数
     */
    params?: any;
}



