import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { LearningMachineVO, LearningMachineForm, LearningMachineQuery } from '@/api/machine/learningMachine/types';

/**
 * 查询学习机管理列表
 * @param query
 * @returns {*}
 */

export const listLearningMachine = (query?: LearningMachineQuery): AxiosPromise<LearningMachineVO[]> => {
  return request({
    url: '/machine/learningMachine/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询学习机管理详细
 * @param machineId
 */
export const getLearningMachine = (machineId: string | number): AxiosPromise<LearningMachineVO> => {
  return request({
    url: '/machine/learningMachine/' + machineId,
    method: 'get'
  });
};

/**
 * 新增学习机管理
 * @param data
 */
export const addLearningMachine = (data: LearningMachineForm) => {
  return request({
    url: '/machine/learningMachine',
    method: 'post',
    data: data
  });
};

/**
 * 修改学习机管理
 * @param data
 */
export const updateLearningMachine = (data: LearningMachineForm) => {
  return request({
    url: '/machine/learningMachine',
    method: 'put',
    data: data
  });
};

/**
 * 删除学习机管理
 * @param machineId
 */
export const delLearningMachine = (machineId: string | number | Array<string | number>) => {
  return request({
    url: '/machine/learningMachine/' + machineId,
    method: 'delete'
  });
};
