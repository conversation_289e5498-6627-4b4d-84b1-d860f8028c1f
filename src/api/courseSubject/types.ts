export interface CourseSubjectVO {
  /**
   * 主键ID
   */
  subjectId: string | number;

  /**
   * 学科编码
   */
  subjectCode: string;

  /**
   * 学科名
   */
  subjectName: string;

  /**
   * 最大试题数量
   */
  questionMax: number;
  
  /**
   * 课程ID
   */
  courseId: string | number;

}

export interface CourseSubjectForm extends BaseEntity {
  /**
   * 主键ID
   */
  subjectId?: string | number;

  /**
   * 学科编码
   */
  subjectCode?: string;

  /**
   * 学科名
   */
  subjectName?: string;

  /**
   * 最大试题数量
   */
  questionMax?: number;

}

export interface CourseSubjectQuery extends PageQuery {

  /**
   * 学科编码
   */
  subjectCode?: string;

  /**
   * 学科名
   */
  subjectName?: string;

  /**
   * 课程id
   */
  courseId?: string | number;

  /**
   * 科目id
   */
  subjectId?: string | number;

    /**
     * 日期范围参数
     */
    params?: any;
}



