import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CourseSubjectVO, CourseSubjectForm, CourseSubjectQuery } from '@/api/courseSubject/types';

/**
 * 查询学科列表
 * @param query
 * @returns {*}
 */

export const listCourseSubject = (query?: CourseSubjectQuery): AxiosPromise<CourseSubjectVO[]> => {
  return request({
    url: '/courseSubject/courseSubject/list',
    method: 'get',
    params: query
  });
};


export const queryListSubject = (query?: CourseSubjectQuery): AxiosPromise<CourseSubjectVO[]> => {
  return request({
    url: '/courseSubject/courseSubject/queryList',
    method: 'get',
    params: query
  });
};


/**
 * 查询学科详细
 * @param subjectId
 */
export const getCourseSubject = (subjectId: string | number): AxiosPromise<CourseSubjectVO> => {
  return request({
    url: '/courseSubject/courseSubject/' + subjectId,
    method: 'get'
  });
};

/**
 * 新增学科
 * @param data
 */
export const addCourseSubject = (data: CourseSubjectForm) => {
  return request({
    url: '/courseSubject/courseSubject',
    method: 'post',
    data: data
  });
};

/**
 * 修改学科
 * @param data
 */
export const updateCourseSubject = (data: CourseSubjectForm) => {
  return request({
    url: '/courseSubject/courseSubject',
    method: 'put',
    data: data
  });
};

/**
 * 删除学科
 * @param subjectId
 */
export const delCourseSubject = (subjectId: string | number | Array<string | number>) => {
  return request({
    url: '/courseSubject/courseSubject/' + subjectId,
    method: 'delete'
  });
};


/**
 * 按照个人查看科目
 * @param userId
 */
export const selectSubjectByUserId = (userId: string | number): AxiosPromise<CourseSubjectVO[]> => {
  return request({
    url: '/courseSubject/courseSubject/selectSubjectByUserId/' + userId,
    method: 'get'
  });
};
