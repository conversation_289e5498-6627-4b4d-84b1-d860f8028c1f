import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MistakeNoteVO, MistakeNoteForm, MistakeNoteQuery,CorrectSaveCmd } from '@/api/jyeoo/mistakeNote/types';

/**
 * 查询jyeoo错题本列表
 * @param query
 * @returns {*}
 */

export const listMistakeNote = (query?: MistakeNoteQuery): AxiosPromise<MistakeNoteVO[]> => {
  return request({
    url: '/jyeoo/mistakeNote/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询jyeoo错题本详细
 * @param noteId
 */
export const getMistakeNote = (noteId: string | number): AxiosPromise<MistakeNoteVO> => {
  return request({
    url: '/jyeoo/mistakeNote/' + noteId,
    method: 'get'
  });
};

/**
 * 新增jyeoo错题本
 * @param data
 */
export const addMistakeNote = (data: MistakeNoteForm) => {
  return request({
    url: '/jyeoo/mistakeNote',
    method: 'post',
    data: data
  });
};
/**
 * 批改作业
 */
export const correctSave = (data: CorrectSaveCmd) => {
  return request({
    url: '/jyeoo/mistakeNote/correctSave',
    method: 'post',
    data: data
  });
};

/**
 * 修改jyeoo错题本
 * @param data
 */
export const updateMistakeNote = (data: MistakeNoteForm) => {
  return request({
    url: '/jyeoo/mistakeNote',
    method: 'put',
    data: data
  });
};

/**
 * 删除jyeoo错题本
 * @param noteId
 */
export const delMistakeNote = (noteId: string | number | Array<string | number>) => {
  return request({
    url: '/jyeoo/mistakeNote/' + noteId,
    method: 'delete'
  });
};
