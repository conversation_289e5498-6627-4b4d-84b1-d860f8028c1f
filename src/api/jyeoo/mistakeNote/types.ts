export interface MistakeNoteVO {
  /**
   * 主键ID
   */
  noteId: string | number;

  /**
   * 申请打印ID
   */
  applyId: string | number;

  /**
   * 试题ID
   */
  paperId: string | number;

  /**
   * jyeoo组卷标识
   */
  jyeooId: string | number;

  /**
   * 会员id
   */
  userId: string | number;

  /**
   * jyeoo科目
   */
  subjectId: string | number;


  /**
   * 错题内容
   */
  content: string;

}

export interface MistakeNoteForm extends BaseEntity {
  /**
   * 主键ID
   */
  noteId?: string | number;

  /**
   * 申请打印ID
   */
  applyId?: string | number;

  /**
   * 试题ID
   */
  paperId?: string | number;

  /**
   * jyeoo组卷标识
   */
  jyeooId?: string | number;

  /**
   * 会员id
   */
  userId?: string | number;

  /**
   * jyeoo科目
   */
  subjectId?: string | number;

}

export interface MistakeNoteQuery extends PageQuery {

    /**
     * 日期范围参数
     */
    params?: any;
}


/** 
 * 
 * 批改作业
*/
export interface CorrectSaveCmd{
  
  /**
   * 申请打印ID
   */
  applyId?: string | number;


  /**
   * 试题id
   */
  paperIds?: string [] | number [];

}

