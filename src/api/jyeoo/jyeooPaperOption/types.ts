export interface JyeooPaperOptionVO {
  /**
   * 主键ID
   */
  optionId: string | number;

  /**
   * 试题ID
   */
  paperId: string | number;

  /**
   * 选项内容
   */
  paperOption: string;

}

export interface JyeooPaperOptionForm extends BaseEntity {
  /**
   * 主键ID
   */
  optionId?: string | number;

  /**
   * 试题ID
   */
  paperId?: string | number;

  /**
   * 选项内容
   */
  paperOption?: string;

}

export interface JyeooPaperOptionQuery extends PageQuery {

    /**
     * 日期范围参数
     */
    params?: any;
}



