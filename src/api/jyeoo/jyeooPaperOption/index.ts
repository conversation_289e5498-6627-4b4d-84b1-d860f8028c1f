import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { JyeooPaperOptionVO, JyeooPaperOptionForm, JyeooPaperOptionQuery } from '@/api/jyeoo/jyeooPaperOption/types';

/**
 * 查询jyeoo试题选项列表
 * @param query
 * @returns {*}
 */

export const listJyeooPaperOption = (query?: JyeooPaperOptionQuery): AxiosPromise<JyeooPaperOptionVO[]> => {
  return request({
    url: '/jyeoo/jyeooPaperOption/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询jyeoo试题选项详细
 * @param optionId
 */
export const getJyeooPaperOption = (optionId: string | number): AxiosPromise<JyeooPaperOptionVO> => {
  return request({
    url: '/jyeoo/jyeooPaperOption/' + optionId,
    method: 'get'
  });
};

/**
 * 新增jyeoo试题选项
 * @param data
 */
export const addJyeooPaperOption = (data: JyeooPaperOptionForm) => {
  return request({
    url: '/jyeoo/jyeooPaperOption',
    method: 'post',
    data: data
  });
};

/**
 * 修改jyeoo试题选项
 * @param data
 */
export const updateJyeooPaperOption = (data: JyeooPaperOptionForm) => {
  return request({
    url: '/jyeoo/jyeooPaperOption',
    method: 'put',
    data: data
  });
};

/**
 * 删除jyeoo试题选项
 * @param optionId
 */
export const delJyeooPaperOption = (optionId: string | number | Array<string | number>) => {
  return request({
    url: '/jyeoo/jyeooPaperOption/' + optionId,
    method: 'delete'
  });
};
