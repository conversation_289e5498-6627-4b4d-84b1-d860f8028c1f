import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { JyeooPaperVO, JyeooPaperForm, JyeooPaperQuery } from '@/api/jyeoo/jyeooPaper/types';

/**
 * 查询jyeoo试题列表
 * @param query
 * @returns {*}
 */

export const listJyeooPaper = (query?: JyeooPaperQuery): AxiosPromise<JyeooPaperVO[]> => {
  return request({
    url: '/jyeoo/jyeooPaper/list',
    method: 'get',
    params: query
  });
};
/**
 * 根据错题ids，查询
 */
export const getbyMistakeIds = (data?: string): AxiosPromise<JyeooPaperVO[]> => {
  return request({
    url: '/jyeoo/jyeooPaper/getbyMistakeIds',
    method: 'post',
    data: data
  });
};

/**
 * 查询jyeoo试题详细
 * @param paperId
 */
export const getJyeooPaper = (paperId: string | number): AxiosPromise<JyeooPaperVO> => {
  return request({
    url: '/jyeoo/jyeooPaper/' + paperId,
    method: 'get'
  });
};

/**
 * 查询jyeoo试题详细
 * @param paperId
 */
export const queryByApplyId = (applyId: string | number): AxiosPromise<JyeooPaperVO> => {
  return request({
    url: '/jyeoo/jyeooPaper/queryByApplyId?applyId=' + applyId,
    method: 'get'
  });
};

export const queryByResourceId = (resourceId: string | number): AxiosPromise<JyeooPaperVO> => {
  return request({
    url: '/jyeoo/jyeooPaper/queryByResourceId?resourceId=' + resourceId,
    method: 'get'
  });
};


/**
 * 新增jyeoo试题
 * @param data
 */
export const addJyeooPaper = (data: JyeooPaperForm) => {
  return request({
    url: '/jyeoo/jyeooPaper',
    method: 'post',
    data: data
  });
};

/**
 * 修改jyeoo试题
 * @param data
 */
export const updateJyeooPaper = (data: JyeooPaperForm) => {
  return request({
    url: '/jyeoo/jyeooPaper',
    method: 'put',
    data: data
  });
};

/**
 * 删除jyeoo试题
 * @param paperId
 */
export const delJyeooPaper = (paperId: string | number | Array<string | number>) => {
  return request({
    url: '/jyeoo/jyeooPaper/' + paperId,
    method: 'delete'
  });
};
