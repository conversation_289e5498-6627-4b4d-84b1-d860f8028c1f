import { JyeooPaperOptionVO} from '@/api/jyeoo/jyeooPaperOption/types';

export interface JyeooPaperVO {
  /**
   * 主键ID
   */
  paperId: string | number;

  /**
   * 申请打印ID
   */
  applyId: string | number;

  /**
   * jyeoo组卷标识
   */
  jyeooId: string | number;

  /**
   * jyeoo大题标题
   */
  jyeooKey: string;

  /**
   * jyeoo试题id
   */
  sid: string | number;

  /**
   * jyeoo科目
   */
  subjectId: string | number;

  /**
   * jyeoo分类
   */
  cate: number;

  /**
   * jyeoo分类
   */
  cateName: string;

  /**
   * jyeoo考题来源
   */
  label: string;

  /**
   * jyeoo考题内容
   */
  content: string;

  /**
   * jyeoo考题分析
   */
  analyse: string;

  /**
   * jyeoo考题答案用#隔开
   */
  answers: string;

  /**
   * jyeoo难度
   */
  degree: number;

  /**
   * jyeoo分值
   */
  score: number;

  /**
   * 选项
   */
  optionVoList: JyeooPaperOptionVO [];

  /**
   * 1：对 2：错
   */
  correct: number;

  /**
   * 	点评
   */
  discuss: string;


  /**
   * 解答
   */
  method: string;

}

export interface JyeooPaperForm extends BaseEntity {
  /**
   * 主键ID
   */
  paperId?: string | number;

  /**
   * 申请打印ID
   */
  applyId?: string | number;

  /**
   * jyeoo组卷标识
   */
  jyeooId?: string | number;

  /**
   * jyeoo大题标题
   */
  jyeooKey?: string;

  /**
   * jyeoo试题id
   */
  sid?: string | number;

  /**
   * jyeoo科目
   */
  subjectId?: string | number;

  /**
   * jyeoo分类
   */
  cate?: number;

  /**
   * jyeoo分类
   */
  cateName?: string;

  /**
   * jyeoo考题来源
   */
  label?: string;

  /**
   * jyeoo考题内容
   */
  content?: string;

  /**
   * jyeoo考题分析
   */
  analyse?: string;

  /**
   * jyeoo考题答案用#隔开
   */
  answers?: string;

  /**
   * jyeoo难度
   */
  degree?: number;

  /**
   * jyeoo分值
   */
  score?: number;

}

export interface JyeooPaperQuery extends PageQuery {

  /**
   * 申请打印ID
   */
  applyId?: string | number;

    /**
     * 日期范围参数
     */
    params?: any;
}



