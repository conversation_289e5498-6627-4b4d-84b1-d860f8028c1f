import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { AboutUsVO, AboutUsForm, AboutUsQuery } from '@/api/aboutUs/types';

/**
 * 查询关于我们列表
 * @param query
 * @returns {*}
 */

export const listAboutUs = (query?: AboutUsQuery): AxiosPromise<AboutUsVO[]> => {
  return request({
    url: '/aboutUs/aboutUs/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询关于我们详细
 * @param usId
 */
export const getAboutUs = (usId: string | number): AxiosPromise<AboutUsVO> => {
  return request({
    url: '/aboutUs/aboutUs/' + usId,
    method: 'get'
  });
};

/**
 * 新增关于我们
 * @param data
 */
export const addAboutUs = (data: AboutUsForm) => {
  return request({
    url: '/aboutUs/aboutUs',
    method: 'post',
    data: data
  });
};

/**
 * 修改关于我们
 * @param data
 */
export const updateAboutUs = (data: AboutUsForm) => {
  return request({
    url: '/aboutUs/aboutUs',
    method: 'put',
    data: data
  });
};

/**
 * 删除关于我们
 * @param usId
 */
export const delAboutUs = (usId: string | number | Array<string | number>) => {
  return request({
    url: '/aboutUs/aboutUs/' + usId,
    method: 'delete'
  });
};
