export interface AboutUsVO {
  /**
   * 主键ID
   */
  usId: string | number;

  /**
   * 描述
   */
  usContent: string;

  /**
   * 1启用，0禁用
   */
  usStatus: string;

}

export interface AboutUsForm extends BaseEntity {
  /**
   * 主键ID
   */
  usId?: string | number;

  /**
   * 描述
   */
  usContent?: string;

  /**
   * 1启用，0禁用
   */
  usStatus?: string;

}

export interface AboutUsQuery extends PageQuery {

    /**
     * 日期范围参数
     */
    params?: any;
}



