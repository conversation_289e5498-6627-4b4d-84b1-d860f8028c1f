export interface CurrenMemberVo {
  /**
   * 总分布
   */
  totalDistribution: Map<string, any>[];

  /**
   * 年级分布
   */
  gradeDistribution: any[];

  /**
   * 当前在读学生数量
   */
  currentMemberCount: number;

  /**
   * 本季度在读学生数量
   */
  quarterMemberCount: number;

  /**
   * 伴学师续费率
   */
  renewalRateData: any[];

  /**
   * 春续量续费率
   */
  springToSummerTxt: string[];

  /**
   * 春续量续费率
   */
  springToSummerPercent: number[];

  /**
   * 春续人数
   */

  springToSummerSum: number[];

  /**
   * 上期数量
   */
  lastNum: number;

  /**
   * 上期已续费
   */
  lastRenewalSum: number;

  /**
   * 上期续费率
   */
  lastRenewal: number;

}