export interface MemberPointsConfigVO {
  /**
   * 主键ID
   */
  configId: string | number;

  /**
   * 类型名称
   */
  configName: string;

  /**
   * 消耗积分数量
   */
  pointsNum: number;

  /**
   * 积分图片
   */
  configImage: string;

  /**
   * 期限
   */
  configTerm: number;

  /**
   * 期限类型
   */
  configType: string;

  /**
   * 顺序
   */
  orderNum: number;

  /**
   * 是否开通菁优
   */
  jyeooStatus: string;

}

export interface MemberPointsConfigForm extends BaseEntity {
  /**
   * 主键ID
   */
  configId?: string | number;

  /**
   * 类型名称
   */
  configName?: string;

  /**
   * 消耗积分数量
   */
  pointsNum?: number;

  /**
   * 积分图片
   */
  configImage?: string;

  /**
   * 期限
   */
  configTerm?: number;

  /**
   * 期限类型
   */
  configType?: string;

  /**
   * 顺序
   */
  orderNum: number;

  /**
   * 是否开通菁优
   */
  jyeooStatus: string;
}

export interface MemberPointsConfigQuery extends PageQuery {

  /**
   * 类型名称
   */
  configName?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



