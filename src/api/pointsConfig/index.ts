import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MemberPointsConfigVO, MemberPointsConfigForm, MemberPointsConfigQuery } from '@/api/pointsConfig/types';

/**
 * 查询会员类型积分设置列表
 * @param query
 * @returns {*}
 */

export const listMemberPointsConfig = (query?: MemberPointsConfigQuery): AxiosPromise<MemberPointsConfigVO[]> => {
  return request({
    url: '/pointsConfig/memberPointsConfig/list',
    method: 'get',
    params: query
  });
};
/**
 * 查询全部
 */

export const pointsQueryList = (query?: MemberPointsConfigQuery): AxiosPromise<MemberPointsConfigVO[]> => {
  return request({
    url: '/pointsConfig/memberPointsConfig/queryList',
    method: 'get',
    params: query
  });
};
/**
 * 查询会员类型积分设置详细
 * @param configId
 */
export const getMemberPointsConfig = (configId: string | number): AxiosPromise<MemberPointsConfigVO> => {
  return request({
    url: '/pointsConfig/memberPointsConfig/' + configId,
    method: 'get'
  });
};

/**
 * 新增会员类型积分设置
 * @param data
 */
export const addMemberPointsConfig = (data: MemberPointsConfigForm) => {
  return request({
    url: '/pointsConfig/memberPointsConfig',
    method: 'post',
    data: data
  });
};

/**
 * 修改会员类型积分设置
 * @param data
 */
export const updateMemberPointsConfig = (data: MemberPointsConfigForm) => {
  return request({
    url: '/pointsConfig/memberPointsConfig',
    method: 'put',
    data: data
  });
};

/**
 * 删除会员类型积分设置
 * @param configId
 */
export const delMemberPointsConfig = (configId: string | number | Array<string | number>) => {
  return request({
    url: '/pointsConfig/memberPointsConfig/' + configId,
    method: 'delete'
  });
};
