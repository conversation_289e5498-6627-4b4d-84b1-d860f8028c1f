import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { StudyRecordVO, StudyRecordForm, StudyRecordQuery } from '@/api/study/studyRecord/types';

/**
 * 查询学习记录列表
 * @param query
 * @returns {*}
 */

export const listStudyRecord = (query?: StudyRecordQuery): AxiosPromise<StudyRecordVO[]> => {
  return request({
    url: '/studyPlan/studyRecord/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询学习记录详细
 * @param recordId
 */
export const getStudyRecord = (recordId: string | number): AxiosPromise<StudyRecordVO> => {
  return request({
    url: '/studyPlan/studyRecord/' + recordId,
    method: 'get'
  });
};

/**
 * 新增学习记录
 * @param data
 */
export const addStudyRecord = (data: StudyRecordForm) => {
  return request({
    url: '/studyPlan/studyRecord',
    method: 'post',
    data: data
  });
};

/**
 * 修改学习记录
 * @param data
 */
export const updateStudyRecord = (data: StudyRecordForm) => {
  return request({
    url: '/studyPlan/studyRecord',
    method: 'put',
    data: data
  });
};

/**
 * 删除学习记录
 * @param recordId
 */
export const delStudyRecord = (recordId: string | number | Array<string | number>) => {
  return request({
    url: '/studyPlan/studyRecord/' + recordId,
    method: 'delete'
  });
};
