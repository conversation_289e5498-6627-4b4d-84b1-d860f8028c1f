export interface StudyRecordVO {
  /**
   * 主键ID
   */
  recordId: string | number;

  /**
   * 会员id
   */
  userId: string | number;

  /**
   * 会员名
   */
  userName: string;

  /**
   * 课程资源id
   */
  courseResourceId: string | number;

  /**
   * 已观看时长分钟
   */
  viewMinute: number;

  /**
   * 已观看时长秒
   */
  viewSecond: number;

  /**
   * 学习内容
   */
  studyContent: string;

  /**
   * 学习昵称
   */
  nickName: string;

  /**
   * 学习时长
   */
  studyTime: string;

  /**
   * 讲义
   */
  courseNotes: string;
}

export interface StudyRecordForm extends BaseEntity {
  /**
   * 主键ID
   */
  recordId?: string | number;

  /**
   * 会员id
   */
  userId?: string | number;

  /**
   * 会员名
   */
  userName?: string;

  /**
   * 课程资源id
   */
  courseResourceId?: string | number;

  /**
   * 已观看时长分钟
   */
  viewMinute?: number;

  /**
   * 已观看时长秒
   */
  viewSecond?: number;
}

export interface StudyRecordQuery extends PageQuery {
  /**
   * 会员id
   */
  userId?: string | number;

  /**
   * 会员名
   */
  userName?: string;

  /**
   * 课程资源id
   */
  courseResourceId?: string | number;

  /**
   * 日期范围参数
   */
  params?: any;

}