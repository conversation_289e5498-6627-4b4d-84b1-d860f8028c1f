import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { StudyPlanVO, StudyPlanForm, StudyPlanQuery,StudyPlanDayQuery,PlanDayQuery } from '@/api/study/studyPlan/types';

/**
 * 查询学习计划列表
 * @param query
 * @returns {*}
 */

export const listStudyPlan = (query?: StudyPlanQuery): AxiosPromise<StudyPlanVO[]> => {
  return request({
    url: '/system/studyPlan/list',
    method: 'get',
    params: query
  });
};

export const queryListStudyPlan = (query?: StudyPlanDayQuery): AxiosPromise<StudyPlanVO[]> => {
  return request({
    url: '/system/studyPlan/queryList',
    method: 'get',
    params: query
  });
};

export const queryPlanSummarize = (query?: StudyPlanDayQuery): AxiosPromise<Map<string,object>> => {
  return request({
    url: '/system/studyPlan/summarize',
    method: 'get',
    params: query
  });
};

export const queryPlanDay = (query?: PlanDayQuery): AxiosPromise<any[]> => {
  return request({
    url: '/system/studyPlan/getPlanDay',
    method: 'get',
    params: query
  });
};

export const getPlanDayMonth = (query?: PlanDayQuery): AxiosPromise<any[]> => {
  return request({
    url: '/system/studyPlan/getPlanDayMonth',
    method: 'get',
    params: query
  });
};


/**
 * 查询学习计划详细
 * @param planId
 */
export const getStudyPlan = (planId: string | number): AxiosPromise<StudyPlanVO> => {
  return request({
    url: '/system/studyPlan/' + planId,
    method: 'get'
  });
};

/**
 * 新增学习计划
 * @param data
 */
export const addStudyPlan = (data: StudyPlanForm) => {
  return request({
    url: '/system/studyPlan',
    method: 'post',
    data: data
  });
};
/**
 * 新增学习计划
 * @param data
 */
export const addStudyPlanSchedule = (data: any) => {
  return request({
    url: '/system/studyPlan/savePlans',
    method: 'post',
    data: data
  });
};

/**
 * 修改学习计划
 * @param data
 */
export const updateStudyPlan = (data: StudyPlanForm) => {
  return request({
    url: '/system/studyPlan',
    method: 'put',
    data: data
  });
};

/**
 * 删除学习计划
 * @param planId
 */
export const delStudyPlan = (planId: string | number | Array<string | number>) => {
  return request({
    url: '/system/studyPlan/' + planId,
    method: 'delete'
  });
};
