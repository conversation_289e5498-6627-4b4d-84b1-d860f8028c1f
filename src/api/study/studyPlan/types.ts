export interface StudyPlanVO {
  /**
   * 主键ID
   */
  planId: string | number;

  /**
   * 日期
   */
  planDate: string;

  /**
   * 会员id
   */
  userId: string | number;

  /**
   * 会员名
   */
  userName: string;

  /**
   * 课程资源id
   */
  courseResourceId: string | number;

  /**
   * 年级
   */
  grade: string;

  /**
   * 伴学师
   */
  studyMentorId: string | number;

  /**
   * 伴学师姓名
   */
  studyMentorName: string;

  /**
   * 到店和登录情况
   */
  attendanceStatus: string;

  /**
   * 排序，按照时间早晚排
   */
  studyOrder: number;

  /**
   * 起始时间
   */
  startTime: string;

  /**
   * 结束时间
   */
  endTime: string;

  /**
   * 学习内容
   */
  studyContent: string;

  /**
   * 内容信息
   */
  contentInfo: string;

  /**
   * 视频时长分钟
   */
  videoMinute: string | number;

  /**
   * 视频时长秒
   */
  videoSecond: string | number;

  /**
   * 学校id
   */
  deptId: string | number;

  /**
   * 昵称
   */
  studyMentorNickName: string;

}

export interface StudyPlanForm extends BaseEntity {
  /**
   * 主键ID
   */
  planId?: string | number;

  /**
   * 日期
   */
  planDate?: string;

  /**
   * 会员id
   */
  userId?: string | number;

  /**
   * 会员名
   */
  userName?: string;

  /**
   * 课程资源id
   */
  courseResourceId?: string | number;

  /**
   * 年级
   */
  grade?: string;

  /**
   * 伴学师
   */
  studyMentorId?: string | number;

  /**
   * 伴学师姓名
   */
  studyMentorName?: string;

  /**
   * 到店和登录情况
   */
  attendanceStatus?: string;

  /**
   * 排序，按照时间早晚排
   */
  studyOrder?: number;

  /**
   * 起始时间
   */
  startTime?: string;

  /**
   * 结束时间
   */
  endTime?: string;

  /**
   * 学习内容
   */
  studyContent?: string;

  /**
   * 内容信息
   */
  contentInfo?: string;

  /**
   * 视频时长分钟
   */
  videoMinute?: string | number;

  /**
   * 视频时长秒
   */
  videoSecond?: string | number;

  /**
   * 学校id
   */
  deptId?: string | number;

}
export interface StudyPlanDayQuery{
  
  planDate?: string,
  /**
   * 学校id
   */
  deptId?: string | number;

  /**
 * 日期范围参数
 */
  params?: any;
}
export interface StudyPlanQuery extends PageQuery {

    /**
     * 日期范围参数
     */
    params?: any;
}


export interface PlanDayQuery{
  yearMonth?: string,//yyyy-MM,
  userIds?: string,//;隔开
}


