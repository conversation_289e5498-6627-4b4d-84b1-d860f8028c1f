import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CourseResourceVO, CourseResourceForm, CourseResourceQuery,CourseResourceQuerySchedule } from '@/api/course/courseResource/types';
import { CourseVO} from '@/api/course/course/types';

/**
 * 查询课程资源列表
 * @param query
 * @returns {*}
 */

export const listCourseResource = (query?: CourseResourceQuery): AxiosPromise<CourseResourceVO[]> => {
  return request({
    url: '/system/courseResource/list',
    method: 'get',
    params: query
  });
};

export const courseSchedule = (query?: CourseResourceQuerySchedule): AxiosPromise<CourseVO[]> => {
  return request({
    url: '/system/courseResource/courseSchedule',
    method: 'get',
    params: query
  });
};


/**
 * 查询课程资源详细
 * @param resourceId
 */
export const getCourseResource = (resourceId: string | number): AxiosPromise<CourseResourceVO> => {
  return request({
    url: '/system/courseResource/' + resourceId,
    method: 'get'
  });
};

/**
 * 新增课程资源
 * @param data
 */
export const addCourseResource = (data: CourseResourceForm) => {
  return request({
    url: '/system/courseResource',
    method: 'post',
    data: data
  });
};

/**
 * 修改课程资源
 * @param data
 */
export const updateCourseResource = (data: CourseResourceForm) => {
  return request({
    url: '/system/courseResource',
    method: 'put',
    data: data
  });
};

/**
 * 删除课程资源
 * @param resourceId
 */
export const delCourseResource = (resourceId: string | number | Array<string | number>) => {
  return request({
    url: '/system/courseResource/' + resourceId,
    method: 'delete'
  });
};
