/**
 * 部门查询参数
 */
export interface DeptQuery extends PageQuery {
  deptName?: string;
  deptCategory?: string;
  status?: number;
}

/**
 * 部门类型
 */
export interface DeptVO extends BaseEntity {
  id: number | string;
  parentName: string;
  parentId: number | string;
  children: DeptVO[];
  deptId: number | string;
  deptName: string;
  deptCategory: string;
  orderNum: number;
  leader: string;
  phone: string;
  email: string;
  status: string;
  delFlag: string;
  ancestors: string;
  menuId: string | number;


  /**
   * 区域层级: province, city, district, school
   */
  areaLevel: string;

  /**
   * 已开账号数量
   */
  openedAccountCount: number;

  /**
   * 已分配账号数量
   */
  dispatchAccountCount: number;

  /**
   * 总账号数量
   */
  accountCount: number;

    /**
   * 可用数量
   */
  availableCount: number;

  /**
   * 季度可用数量
   */
  quarterAvailableCount: number;
  
}

/**
 * 部门表单类型
 */
export interface DeptForm {
  parentName?: string;
  parentId?: number | string;
  children?: DeptForm[];
  deptId?: number | string;
  deptName?: string;
  deptCategory?: string;
  orderNum?: number;
  leader?: string;
  phone?: string;
  email?: string;
  status?: string;
  delFlag?: string;
  ancestors?: string;


  /**
   * 区域层级: province, city, district, school
   */
  areaLevel?: string;

  /**
   * 已开账号数量
   */
  openedAccountCount?: number;

  /**
   * 已分配账号数量
   */
  dispatchAccountCount?: number;

  /**
   * 总账号数量
   */
  accountCount?: number;
}

export interface AuthDeptForm {

  targetDeptId?: number | string;

  targetDeptName?: string;
  /**
   * 分配账号数量
   */
  changeCount?: number;

    /**
   * 剩余账号数量
   */
  availableCount?: number;

  ownDeptName? :string;

  ownDeptId? : number | string;

  authType? :String;

  quarterAvailableCount?: number;
}