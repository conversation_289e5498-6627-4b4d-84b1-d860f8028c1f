import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DeptDispatchHisVO, DeptDispatchHisForm, DeptDispatchHisQuery } from '@/api/system/deptDispatchHis/types';

/**
 * 查询账号分配历史列表
 * @param query
 * @returns {*}
 */

export const listDeptDispatchHis = (query?: DeptDispatchHisQuery): AxiosPromise<DeptDispatchHisVO[]> => {
  return request({
    url: '/system/deptDispatchHis/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询账号分配历史详细
 * @param hisId
 */
export const getDeptDispatchHis = (hisId: string | number): AxiosPromise<DeptDispatchHisVO> => {
  return request({
    url: '/system/deptDispatchHis/' + hisId,
    method: 'get'
  });
};

/**
 * 新增账号分配历史
 * @param data
 */
export const addDeptDispatchHis = (data: DeptDispatchHisForm) => {
  return request({
    url: '/system/deptDispatchHis',
    method: 'post',
    data: data
  });
};

/**
 * 修改账号分配历史
 * @param data
 */
export const updateDeptDispatchHis = (data: DeptDispatchHisForm) => {
  return request({
    url: '/system/deptDispatchHis',
    method: 'put',
    data: data
  });
};

/**
 * 删除账号分配历史
 * @param hisId
 */
export const delDeptDispatchHis = (hisId: string | number | Array<string | number>) => {
  return request({
    url: '/system/deptDispatchHis/' + hisId,
    method: 'delete'
  });
};
