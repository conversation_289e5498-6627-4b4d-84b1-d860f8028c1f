export interface DeptDispatchHisVO {
  /**
   * 主键
   */
  hisId: string | number;

  /**
   * 部门id
   */
  deptId: string | number;

  /**
   * 用户id
   */
  userId: string | number;

  /**
   * 目标部门id
   */
  targetDeptId: string | number;

  /**
   * 变化数量
   */
  changeCount: number;

  /**
   * 原有总数量
   */
  originCount: number;

  /**
   * 变化后总数量
   */
  targetCount: number;

}

export interface DeptDispatchHisForm extends BaseEntity {
  /**
   * 主键
   */
  hisId?: string | number;

  /**
   * 部门id
   */
  deptId?: string | number;

  /**
   * 用户id
   */
  userId?: string | number;

  /**
   * 目标部门id
   */
  targetDeptId?: string | number;

  /**
   * 变化数量
   */
  changeCount?: number;

  /**
   * 原有总数量
   */
  originCount?: number;

  /**
   * 变化后总数量
   */
  targetCount?: number;

}

export interface DeptDispatchHisQuery extends PageQuery {

  /**
   * 部门id
   */
  deptId?: string | number;

  /**
   * 用户id
   */
  userId?: string | number;

  /**
   * 目标部门id
   */
  targetDeptId?: string | number;

  /**
   * 变化数量
   */
  changeCount?: number;

  /**
   * 原有总数量
   */
  originCount?: number;

  /**
   * 变化后总数量
   */
  targetCount?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}



