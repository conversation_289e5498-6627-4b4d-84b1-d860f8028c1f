export interface DeptConsumeHisVO {
   /**
   * 主键
   */
  hisId: string | number;

  /**
   * 用户id(学生id)
   */
  userId: string | number;

  /**
   * 部门id(大部分是学校)
   */
  deptId: string | number;

  /**
   * 变化数量
   */
  changeCount: number;

  /**
   * 原有总数量
   */
  originCount: number;

  /**
   * 会员姓名
   */
  nickName: string;

  /**
   * 会员账号
   */
  userName: string;

  /**
   * 变化后总数量
   */
  targetCount: number;

  /**
   * 授权类型来自ci_member_points_config表
   */
  authType: number;

  /**
   * 授权类型名称来自ci_member_points_config表
   */
  configName: string;
}

export interface DeptConsumeHisForm extends BaseEntity {
  /**
   * 用户id(学生id)
   */
  userId?: string | number;

  /**
   * 部门id(大部分是学校)
   */
  deptId?: string | number;

  /**
   * 变化数量
   */
  changeCount?: number;

  /**
   * 原有总数量
   */
  originCount?: number;

  /**
   * 会员姓名
   */
  nickName?: string;

  /**
   * 会员账号
   */
  userName?: string;

  /**
   * 变化后总数量
   */
  targetCount?: number;

  /**
   * 授权类型来自ci_member_points_config表
   */
  authType?: number;

    /**
     * 日期范围参数
     */
    params?: any;

}



