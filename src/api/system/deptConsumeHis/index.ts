import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DeptConsumeHisVO, DeptConsumeHisForm, DeptConsumeHisQuery } from '@/api/system/deptConsumeHis/types';

/**
 * 查询学校账号消耗记录列表
 * @param query
 * @returns {*}
 */

export const listDeptConsumeHis = (query?: DeptConsumeHisQuery): AxiosPromise<DeptConsumeHisVO[]> => {
  return request({
    url: '/system/deptConsumeHis/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询学校账号消耗记录详细
 * @param hisId
 */
export const getDeptConsumeHis = (hisId: string | number): AxiosPromise<DeptConsumeHisVO> => {
  return request({
    url: '/system/deptConsumeHis/' + hisId,
    method: 'get'
  });
};

/**
 * 新增学校账号消耗记录
 * @param data
 */
export const addDeptConsumeHis = (data: DeptConsumeHisForm) => {
  return request({
    url: '/system/deptConsumeHis',
    method: 'post',
    data: data
  });
};

/**
 * 修改学校账号消耗记录
 * @param data
 */
export const updateDeptConsumeHis = (data: DeptConsumeHisForm) => {
  return request({
    url: '/system/deptConsumeHis',
    method: 'put',
    data: data
  });
};

/**
 * 删除学校账号消耗记录
 * @param hisId
 */
export const delDeptConsumeHis = (hisId: string | number | Array<string | number>) => {
  return request({
    url: '/system/deptConsumeHis/' + hisId,
    method: 'delete'
  });
};
