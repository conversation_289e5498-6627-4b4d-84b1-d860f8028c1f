import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { AreaVO, AreaForm, AreaQuery } from '@/api/system/area/types';

/**
 * 查询行政区码列表
 * @param query
 * @returns {*}
 */

export const listArea = (query?: AreaQuery): AxiosPromise<AreaVO[]> => {
  return request({
    url: '/system/area/list',
    method: 'get',
    params: query
  });
};
// 查询行政区码列表
export function listAreaF(query) {
  return request({
    url: '/system/area/list',
    method: 'get',
    params: query
  });
}
/**
 * 查询行政区码详细
 * @param id
 */
export const getArea = (id: string | number): AxiosPromise<AreaVO> => {
  return request({
    url: '/system/area/' + id,
    method: 'get'
  });
};

/**
 * 新增行政区码
 * @param data
 */
export const addArea = (data: AreaForm) => {
  return request({
    url: '/system/area',
    method: 'post',
    data: data
  });
};

/**
 * 修改行政区码
 * @param data
 */
export const updateArea = (data: AreaForm) => {
  return request({
    url: '/system/area',
    method: 'put',
    data: data
  });
};

/**
 * 删除行政区码
 * @param id
 */
export const delArea = (id: string | number | Array<string | number>) => {
  return request({
    url: '/system/area/' + id,
    method: 'delete'
  });
};
