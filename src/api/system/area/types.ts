export interface AreaVO {
  /**
   * 区域ID
   */
  id: string | number;

  /**
   * 区域编码
   */
  code: string;

  /**
   * 简称
   */
  simpleName: string;

  /**
   * 上级区域
   */
  parentId: string | number;

  /**
   * 上级区域编码
   */
  parentCode: string;

  /**
   * 区域名称
   */
  name: string;

  /**
   * 区域层级
   */
  level: number;

  /**
   * 区域数字
   */
  num: number;

  /**
   * 区域状态：0:不可用，1:可用
   */
  status: number;

  /**
   * 使用说明
   */
  remark: string;

}

export interface AreaForm extends BaseEntity {
  /**
   * 区域ID
   */
  id?: string | number;

  /**
   * 区域编码
   */
  code?: string;

  /**
   * 简称
   */
  simpleName?: string;

  /**
   * 上级区域
   */
  parentId?: string | number;

  /**
   * 上级区域编码
   */
  parentCode?: string;

  /**
   * 区域名称
   */
  name?: string;

  /**
   * 区域层级
   */
  level?: number;

  /**
   * 区域数字
   */
  num?: number;

  /**
   * 区域状态：0:不可用，1:可用
   */
  status?: number;

  /**
   * 使用说明
   */
  remark?: string;

}

export interface AreaQuery extends PageQuery {

  /**
   * 区域编码
   */
  code?: string;

  /**
   * 简称
   */
  simpleName?: string;

  /**
   * 上级区域
   */
  parentId?: string | number;

  /**
   * 上级区域编码
   */
  parentCode?: string;

  /**
   * 区域名称
   */
  name?: string;

  /**
   * 区域层级
   */
  level?: number;

  /**
   * 区域数字
   */
  num?: number;

  /**
   * 区域状态：0:不可用，1:可用
   */
  status?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}



