import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MemberVO, MemberForm, MemberQuery,MemberClassTypeForm,MemberQueryPinyin } from '@/api/system/member/types';
import { MistakeNoteVO} from '@/api/jyeoo/mistakeNote/types';

/**
 * 查询用户信息列表
 * @param query
 * @returns {*}
 */

export const listMember = (query?: MemberQuery): AxiosPromise<MemberVO[]> => {
  return request({
    url: '/system/member/list',
    method: 'get',
    params: query
  });
};

/**
 * 下拉框用
 * @param query
 * @returns {*}
 */
export const optionMember = (): AxiosPromise<MemberVO[]> => {
  return request({
    url: '/system/member/queryOptionList',
    method: 'get'
  });
};

/**
 * 会员列表，排课用
 * 
 */

export const listMembersPinyin = (query?: MemberQueryPinyin): AxiosPromise<Map<string, MemberVO[]>> => {
  return request({
    url: '/system/member/listMembersPinyin',
    method: 'get',
    params: query
  });
};

/**
 * 查询用户信息详细
 * @param userId
 */
export const getMember = (userId: string | number): AxiosPromise<MemberVO> => {
  return request({
    url: '/system/member/' + userId,
    method: 'get'
  });
};

/**
 * 错题本
 */
export const mistakeQueryByUserId = (userId: string | number,subjectId: string | number): AxiosPromise<MistakeNoteVO[]> => {
  return request({
    url: '/member/mistake/queryByUserId/'+userId+"?subjectId="+subjectId,
    method: 'get'
  });
};
/**
 * 新增用户信息
 * @param data
 */
export const addMember = (data: MemberForm) => {
  return request({
    url: '/system/member',
    method: 'post',
    data: data
  });
};
/**
 * 开户Jeyoo
 * @param data
 */
export const registerJeyoo = (data: MemberForm) => {
  return request({
    url: '/system/member/registerJeyoo',
    method: 'post',
    data: data
  });
};
/**
 * 修改用户信息
 * @param data
 */
export const updateMember = (data: MemberForm) => {
  return request({
    url: '/system/member',
    method: 'put',
    data: data
  });
};
/**
 * 授权用户信息
 * @param data
 */
export const authMember = (data: MemberForm) => {
  return request({
    url: '/system/member/auth',
    method: 'put',
    data: data
  });
};
/**
 * 设置班型
 * @param data
 */
export const setClassType = (data: MemberClassTypeForm) => {
  return request({
    url: '/system/member/setClassType',
    method: 'put',
    data: data
  });
};
/**
 * 删除用户信息
 * @param userId
 */
export const delMember = (userId: string | number | Array<string | number>) => {
  return request({
    url: '/system/member/' + userId,
    method: 'delete'
  });
};
/**
 * 用户状态修改
 * @param userId 用户ID
 * @param status 用户状态
 */
export const changeMemberStatus = (userId: number | string, status: string) => {
  const data = {
    userId,
    status
  };
  return request({
    url: '/system/member/changeStatus',
    method: 'put',
    data: data
  });
};
