export interface MemberVO {
  /**
   * 用户ID
   */
  userId: string | number;

  /**
   * 部门ID
   */
  deptId: string | number;

  /**
   * 用户账号
   */
  userName: string;

  /**
   * 用户昵称
   */
  nickName: string;

  /**
   * 用户类型（sys_user系统用户,member 会员）
   */
  userType: string;

  /**
   * 用户邮箱
   */
  email: string;

  /**
   * 手机号码
   */
  phonenumber: string;

  /**
   * 用户性别（0男 1女 2未知）
   */
  sex: string;

  /**
   * 头像地址
   */
  avatar: number;

  /**
   * 密码
   */
  password: string;

  /**
   * 帐号状态（0正常 1停用）
   */
  status: string;

  /**
   * 最后登录IP
   */
  loginIp: string;

  /**
   * 最后登录时间
   */
  loginDate: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 账号类型 trial:体验会员,regular:普通会员
   */
  memberType: string;

  /**
   * 起始时间
   */
  startTime: string;

  /**
   * 结束时间
   */
  endTime: string;

  /**
   * 年级
   */
  grade: string;

  /**
   * 学生来源
   */
  source: string;

  /**
   * 家长电话
   */
  parentPhoneFirst: string;

  /**
   * 家长电话
   */
  parentPhoneSecond: string;

  /**
   * 伴学师
   */
  studyMentorId: string | number;

  /**
   * 伴学师姓名
   */
  studyMentorName: string;

  /**
   * 家庭住址
   */
  homeAddress: string;

  /**
   * 省份
   */
  provinceCode: string;

  /**
   * 市编码
   */
  cityCode: string;

  /**
   * 区
   */
  districtCode: string;

  /**
   * 在校班级
   */
  classSchool: string;

  /**
   * 公立学校学号
   */
  studentNum: string;

  /**
   * 文理科
   */
  sciences: string;

  /**
   * 住校情况
   */
  boardingStatus: string;

  /**
   * 家长openId
   */
  parentOpenid: string | number;

  /**
   * 在读学校
   */
  currSchool: number;

}

export interface MemberForm extends BaseEntity {
  /**
   * 用户ID
   */
  userId?: string | number;

  /**
   * 部门ID
   */
  deptId?: string | number;

  /**
   * 用户账号
   */
  userName?: string;

  /**
   * 用户昵称
   */
  nickName?: string;

  /**
   * 用户类型（sys_user系统用户,member 会员）
   */
  userType?: string;

  /**
   * 用户邮箱
   */
  email?: string;

  /**
   * 手机号码
   */
  phonenumber?: string;

  /**
   * 用户性别（0男 1女 2未知）
   */
  sex?: string;

  /**
   * 头像地址
   */
  avatar?: number;

  /**
   * 密码
   */
  password?: string;

  /**
   * 帐号状态（0正常 1停用）
   */
  status?: string;

  /**
   * 最后登录IP
   */
  loginIp?: string;

  /**
   * 最后登录时间
   */
  loginDate?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 账号类型 trial:体验会员,regular:普通会员
   */
  memberType?: string;

  /**
   * 起始时间
   */
  startTime?: string;

  /**
   * 结束时间
   */
  endTime?: string;

  /**
   * 年级
   */
  grade?: string;

  /**
   * 学生来源
   */
  source?: string;

  /**
   * 家长电话
   */
  parentPhoneFirst?: string;

  /**
   * 家长电话
   */
  parentPhoneSecond?: string;

  /**
   * 伴学师
   */
  studyMentorId?: string | number;

  /**
   * 伴学师姓名
   */
  studyMentorName?: string;

  /**
   * 家庭住址
   */
  homeAddress?: string;

  /**
   * 省份
   */
  provinceCode?: string;

  /**
   * 市编码
   */
  cityCode?: string;

  /**
   * 区
   */
  districtCode?: string;

  /**
   * 在校班级
   */
  classSchool?: string;

  /**
   * 公立学校学号
   */
  studentNum?: string;

  /**
   * 文理科
   */
  sciences?: string;

  /**
   * 住校情况
   */
  boardingStatus?: string;

  /**
   * 家长openId
   */
  parentOpenid?: string | number;

  /**
   * 在读学校
   */
  currSchool?: number;
  
    /**
   * 年度剩余账号
   */
  availableCount: number;

  //会员授权起始日期
  startTimeNew: string;

  //会员授权截止日期
  endTimeNew: string;

    /**
   * 季度剩余账号
   */
  quarterAvailableCount?: number;

  /**
   * authType授权类型
   * 
   * **/
  authType?: string;
}

export interface MemberQuery extends PageQuery {

  /**
   * 用户账号
   */
  userName?: string;

  /**
   * 手机号码
   */
  phonenumber?: string;

  /**
   * 年级
   */
  grade?: string;

  /**
   * 学生来源
   */
  source?: string;

  /**
   * 家长电话
   */
  parentPhoneFirst?: string;

  /**
   * 伴学师姓名
   */
  studyMentorName?: string;

  deptId: string|number;

  /**
   * 日期范围参数
   */
  params?: any;
}


export interface MemberQueryPinyin {

  /**
   * 用户账号
   */
  nickName?: string;
}

export interface MemberClassTypeForm{
  userId: string|number;
  typeIds: (string | number)[]
}



