export interface UserClassTypeVO {
  /**
   * 用户ID
   */
  typeId: string | number;

  /**
   * 用户ID
   */
  userId: string | number;

}

export interface UserClassTypeForm extends BaseEntity {
  /**
   * 用户ID
   */
  typeId?: string | number;

  /**
   * 用户ID
   */
  userId?: string | number;

}

export interface UserClassTypeQuery extends PageQuery {
    /**
   * 用户ID
   */
    typeId?: string | number;

    /**
     * 用户ID
     */
    userId?: string | number;

    /**
     * 日期范围参数
     */
    params?: any;
}



