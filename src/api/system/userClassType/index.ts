import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { UserClassTypeVO, UserClassTypeForm, UserClassTypeQuery } from '@/api/system/userClassType/types';

/**
 * 查询会员班型列表
 * @param query
 * @returns {*}
 */

export const listUserClassType = (query?: UserClassTypeQuery): AxiosPromise<UserClassTypeVO[]> => {
  return request({
    url: '/system/userClassType/list',
    method: 'get',
    params: query
  });
};

export const queryListUserClassType = (query?: UserClassTypeQuery): AxiosPromise<UserClassTypeVO[]> => {
  return request({
    url: '/system/userClassType/queryList',
    method: 'get',
    params: query
  });
};

/**
 * 查询会员班型详细
 * @param typeId
 */
export const getUserClassType = (typeId: string | number): AxiosPromise<UserClassTypeVO> => {
  return request({
    url: '/system/userClassType/' + typeId,
    method: 'get'
  });
};

/**
 * 新增会员班型
 * @param data
 */
export const addUserClassType = (data: UserClassTypeForm) => {
  return request({
    url: '/system/userClassType',
    method: 'post',
    data: data
  });
};

/**
 * 修改会员班型
 * @param data
 */
export const updateUserClassType = (data: UserClassTypeForm) => {
  return request({
    url: '/system/userClassType',
    method: 'put',
    data: data
  });
};

/**
 * 删除会员班型
 * @param typeId
 */
export const delUserClassType = (typeId: string | number | Array<string | number>) => {
  return request({
    url: '/system/userClassType/' + typeId,
    method: 'delete'
  });
};
