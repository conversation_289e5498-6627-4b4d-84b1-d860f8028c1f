import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { AreaSchoolVO, AreaSchoolForm, AreaSchoolQuery } from '@/api/system/areaschool/types';

/**
 * 查询区域学校列表
 * @param query
 * @returns {*}
 */

export const listAreaSchool = (query?: AreaSchoolQuery): AxiosPromise<AreaSchoolVO[]> => {
  return request({
    url: '/system/areaSchool/list',
    method: 'get',
    params: query
  });
};

// 查询学校
export function listAreaSchoolF(query) {
  return request({
    url: '/system/areaSchool/list',
    method: 'get',
    params: query
  });
}

/**
 * 查询区域学校详细
 * @param id
 */
export const getAreaSchool = (id: string | number): AxiosPromise<AreaSchoolVO> => {
  return request({
    url: '/system/areaSchool/' + id,
    method: 'get'
  });
};

/**
 * 新增区域学校
 * @param data
 */
export const addAreaSchool = (data: AreaSchoolForm) => {
  return request({
    url: '/system/areaSchool',
    method: 'post',
    data: data
  });
};

/**
 * 修改区域学校
 * @param data
 */
export const updateAreaSchool = (data: AreaSchoolForm) => {
  return request({
    url: '/system/areaSchool',
    method: 'put',
    data: data
  });
};

/**
 * 删除区域学校
 * @param id
 */
export const delAreaSchool = (id: string | number | Array<string | number>) => {
  return request({
    url: '/system/areaSchool/' + id,
    method: 'delete'
  });
};
