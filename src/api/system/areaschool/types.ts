export interface AreaSchoolVO {
  /**
   * 区域ID
   */
  id: string | number;

  /**
   * 区域编码
   */
  areaCode: string;

  /**
   * 学校名称
   */
  schoolName: string;

}

export interface AreaSchoolForm extends BaseEntity {
  /**
   * 区域ID
   */
  id?: string | number;

  /**
   * 区域编码
   */
  areaCode?: string;

  /**
   * 学校名称
   */
  schoolName?: string;

}

export interface AreaSchoolQuery extends PageQuery {

  /**
   * 区域编码
   */
  areaCode?: string;

  /**
   * 学校名称
   */
  schoolName?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



