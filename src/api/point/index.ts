import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { PointVO, PointForm, PointQuery } from '@/api/point/types';

/**
 * 查询考点管理列表
 * @param query
 * @returns {*}
 */

export const listPoint = (query?: PointQuery): AxiosPromise<PointVO[]> => {
  return request({
    url: '/point/point/list',
    method: 'get',
    params: query
  });
};

export const queryPointList = (query?: PointQuery): AxiosPromise<PointVO[]> => {
  return request({
    url: '/point/point/queryList',
    method: 'get',
    params: query
  });
};

/**
 * 查询考点管理详细
 * @param pointId
 */
export const getPoint = (pointId: string | number): AxiosPromise<PointVO> => {
  return request({
    url: '/point/point/' + pointId,
    method: 'get'
  });
};

/**
 * 新增考点管理
 * @param data
 */
export const addPoint = (data: PointForm) => {
  return request({
    url: '/point/point',
    method: 'post',
    data: data
  });
};

/**
 * 修改考点管理
 * @param data
 */
export const updatePoint = (data: PointForm) => {
  return request({
    url: '/point/point',
    method: 'put',
    data: data
  });
};

/**
 * 删除考点管理
 * @param pointId
 */
export const delPoint = (pointId: string | number | Array<string | number>) => {
  return request({
    url: '/point/point/' + pointId,
    method: 'delete'
  });
};
