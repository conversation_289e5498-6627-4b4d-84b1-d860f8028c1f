export interface PointVO {
  /**
   * 主键ID
   */
  pointId: string | number;

  /**
   * 考点编号
   */
  pointNo: string;

  /**
   * 学科名称
   */
  pointName: string;

  /**
   * 描述
   */
  pointDesc: string;

  /**
   * 科目主键ID
   */
  subjectId: string | number;

  /**
   * 父考点id
   */
  parentId: string | number;

}

export interface PointForm extends BaseEntity {
  /**
   * 主键ID
   */
  pointId?: string | number;

  /**
   * 考点编号
   */
  pointNo?: string;

  /**
   * 学科名称
   */
  pointName?: string;

  /**
   * 描述
   */
  pointDesc?: string;

  /**
   * 科目主键ID
   */
  subjectId?: string | number;

  /**
   * 父考点id
   */
  parentId?: string | number;

}

export interface PointQuery extends PageQuery {

  /**
   * 考点编号
   */
  pointNo?: string;

  /**
   * 学科名称
   */
  pointName?: string;

  /**
   * 父考点id
   */
  parentId?: string | number;
  /**
   * 科目
   */
  subjectId?: string | number;

    /**
     * 日期范围参数
     */
    params?: any;
}



