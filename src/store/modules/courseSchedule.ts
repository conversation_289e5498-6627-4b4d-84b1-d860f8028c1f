import { defineStore } from 'pinia';
import { CourseVO } from '@/api/course/course/types';

export const courseScheduleStore = defineStore('courses', () => {
  const state = reactive({
    courses: [] as CourseVO[]
  });

  const addCourse = (course: CourseVO) => {
    state.courses.push(course);
  };

  const removeCourse = (course: CourseVO) => {
    state.courses.splice(state.courses.indexOf(course), 1);
  };

  const readAll = () => {
   return state.courses;
  };

  const clearCourse = () => {
    state.courses = [];
  };
  return {
    state,
    addCourse,
    removeCourse,
    readAll,
    clearCourse
  };
});

export default courseScheduleStore;
