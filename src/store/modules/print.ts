import { defineStore } from 'pinia';

interface PrintState {
  selectedNoteIds: (string | number)[];
}

export const usePrintStore = defineStore('print', {
  state: (): PrintState => ({
    selectedNoteIds: []
  }),
  actions: {
    setSelectedNoteIds(noteIds: (string | number)[]) {
      this.selectedNoteIds = noteIds;
    },
    clearSelectedNoteIds() {
      this.selectedNoteIds = [];
    }
  },
  getters: {
    getSelectedNoteIds(): (string | number)[] {
      return this.selectedNoteIds;
    }
  }
}); 