<template>
  <div>
    <el-image :src="realSrc" fit="cover" :style="`width:${realWidth};height:${realHeight};`" :preview-src-list="realSrcList" preview-teleported>
      <template #error>
        <div class="image-slot">
          <i class="el-icon-picture-outline"></i>
        </div>
      </template>
    </el-image>
    <div v-if="showName" :style="`width: ${realWidth};overflow:hidden;text-overflow:ellipsis;white-space: nowrap;`">{{ imageTitle }}</div>
  </div>
</template>

<script lang="ts">
import { listByIds } from '@/api/system/oss';

export default {
  name: 'ImageShow',
  props: {
    src: {
      type: String,
      default: ''
    },
    width: {
      type: [Number, String],
      default: ''
    },
    height: {
      type: [Number, String],
      default: ''
    },
    showName: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      realSrc: '',
      imageTitle: '',
      realSrcList: []
    };
  },
  computed: {
    realWidth() {
      return typeof this.width == 'string' ? this.width : `${this.width}px`;
    },
    realHeight() {
      return typeof this.height == 'string' ? this.height : `${this.height}px`;
    }
  },
  watch: {
    src: {
      async handler(val) {
        if (val) {
          let real_src = val.split(',')[0];
          // 首先将值转为数组
          let list;
          await listByIds(real_src).then((res) => {
            // 首先将值转为数组
            list = res.data;
          });
          if (list && list.length > 0) {
            this.imageTitle = list[0].originalName;
            this.realSrc = list[0].url;
            this.realSrcList.push(list[0].url);
          }
        } else {
          this.imageTitle = '';
          this.realSrc = '';
        }
      },
      deep: true,
      immediate: true
    }
  }
};
</script>
