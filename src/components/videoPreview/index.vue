<template>
  <div>
    <video v-if="realSrc" :src="realSrc" :style="`width:${realWidth};height:${realHeight};`" controls>您的浏览器不支持视频播放。</video>
    <div
      v-else
      class="video-slot"
      :style="`width:${realWidth};height:${realHeight};display:flex;align-items:center;justify-content:center;background:#f5f5f5;`"
    >
      <i class="el-icon-video-camera"></i>
    </div>
    <div v-if="showName" :style="`width: ${realWidth}; overflow:hidden; text-overflow:ellipsis; white-space: nowrap;`">
      {{ videoTitle }}
    </div>
  </div>
</template>

<script lang="ts">
import { listByIds } from '@/api/system/oss';

export default {
  name: 'VideoShow',
  props: {
    src: {
      type: String,
      default: ''
    },
    width: {
      type: [Number, String],
      default: ''
    },
    height: {
      type: [Number, String],
      default: ''
    },
    showName: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      realSrc: '',
      videoTitle: ''
    };
  },
  computed: {
    realWidth() {
      return typeof this.width === 'string' ? this.width : `${this.width}px`;
    },
    realHeight() {
      return typeof this.height === 'string' ? this.height : `${this.height}px`;
    }
  },
  watch: {
    src: {
      async handler(val) {
        if (val) {
          const real_src = val.split(',')[0];
          let list;
          await listByIds(real_src).then((res) => {
            list = res.data;
          });
          if (list && list.length > 0) {
            this.videoTitle = list[0].originalName;
            this.realSrc = list[0].url;
          }
        } else {
          this.videoTitle = '';
          this.realSrc = '';
        }
      },
      deep: true,
      immediate: true
    }
  }
};
</script>
