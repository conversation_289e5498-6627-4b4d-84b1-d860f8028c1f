<template>
  <div class="p-2">
    <transition v-if="!userId" :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="会员名" prop="nickName">
              <el-input v-model="queryParams.nickName" placeholder="会员名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template v-if="!userId"   #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['studyPlan:studyRecord:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="studyRecordList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="会员名" align="center" prop="nickName" />
        <el-table-column label="年级" align="center" prop="grade" />
        <el-table-column label="伴学师" align="center" prop="studyMentorNickName" />
        <el-table-column label="学习内容" align="center" prop="studyContent" />
        <el-table-column label="已观看时长" align="center" prop="studyTime" />
        <el-table-column label="完播率" align="center" prop="completionRateTxt" />
        <el-table-column label="记录时间" align="center" prop="createTime" width="200"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="打印讲义" placement="top">
              <el-button link type="primary" icon="printer" @click="handleUpdatePrintPdf(scope.row)" v-hasPermi="['studyPlan:studyRecord:print']"></el-button>
            </el-tooltip>
            <el-tooltip content="打印试题" placement="top">
              <el-button link type="primary" icon="printer" @click="handlePrint(scope.row)" v-hasPermi="['studyPlan:studyRecord:print']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改学习记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="studyRecordFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="会员id" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入会员id" />
        </el-form-item>
        <el-form-item label="会员名" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入会员名" />
        </el-form-item>
        <el-form-item label="课程资源id" prop="courseResourceId">
          <el-input v-model="form.courseResourceId" placeholder="请输入课程资源id" />
        </el-form-item>
        <el-form-item label="已观看时长分钟" prop="viewMinute">
          <el-input v-model="form.viewMinute" placeholder="请输入已观看时长分钟" />
        </el-form-item>
        <el-form-item label="已观看时长秒" prop="viewSecond">
          <el-input v-model="form.viewSecond" placeholder="请输入已观看时长秒" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="StudyRecord" lang="ts">
import { listStudyRecord, getStudyRecord, delStudyRecord, addStudyRecord, updateStudyRecord } from '@/api/study/studyRecord';
import { StudyRecordVO, StudyRecordQuery, StudyRecordForm } from '@/api/study/studyRecord/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const studyRecordList = ref<StudyRecordVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const studyRecordFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: StudyRecordForm = {
  recordId: undefined,
  userId: undefined,
  userName: undefined,
  courseResourceId: undefined,
  viewMinute: undefined,
  viewSecond: undefined,
}
const data = reactive<PageData<StudyRecordForm, StudyRecordQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userId: undefined,
    userName: undefined,
    courseResourceId: undefined,
    params: {
    }
  },
  rules: {
    userId: [
      { required: true, message: "会员id不能为空", trigger: "blur" }
    ],
    courseResourceId: [
      { required: true, message: "课程资源id不能为空", trigger: "blur" }
    ],
    viewMinute: [
      { required: true, message: "已观看时长分钟不能为空", trigger: "blur" }
    ],
    viewSecond: [
      { required: true, message: "已观看时长秒不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

const props = defineProps({
  param: { type: null, required: true }
});
const userId = computed(() => props.param);
/** 查询学习记录列表 */
const getList = async () => {
  loading.value = true;
  if(userId && userId.value){
    queryParams.value.userId= userId.value;
  }
  const res = await listStudyRecord(queryParams.value);
  studyRecordList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  studyRecordFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: StudyRecordVO[]) => {
  ids.value = selection.map(item => item.recordId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加学习记录";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: StudyRecordVO) => {
  reset();
  const _recordId = row?.recordId || ids.value[0]
  const res = await getStudyRecord(_recordId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改学习记录";
}

/** 提交按钮 */
const submitForm = () => {
  studyRecordFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.recordId) {
        await updateStudyRecord(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addStudyRecord(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: StudyRecordVO) => {
  const _recordIds = row?.recordId || ids.value;
  await proxy?.$modal.confirm('是否确认删除学习记录编号为"' + _recordIds + '"的数据项？').finally(() => loading.value = false);
  await delStudyRecord(_recordIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('studyPlan/studyRecord/export', {
    ...queryParams.value
  }, `studyRecord_${new Date().getTime()}.xlsx`)
}
/** 预览试题 */
const handlePrint = async (row?: CourseResourceVO) => {
  const _resourceId = row?.courseResourceId || ids.value[0];
  const fullPath = proxy.$router.resolve({
    path: '/print-apply/course-resource-paper',
    query: { action: 'add', resourceId: _resourceId }
  }).href;
  window.open(fullPath, '_blank');
}
            
const handleUpdatePrintPdf = async (row?: PrintApplyVO) => {
  window.open(row.courseNotes, '_blank');
}
onMounted(() => {
  getList();
});
</script>
