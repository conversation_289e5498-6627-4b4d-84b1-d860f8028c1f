<template>
  <div class="p-2"  v-loading="loading">
    <el-tabs v-model="activeTab">
          <el-tab-pane label="单人排课" name="single">
            <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
              <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                  <el-form ref="queryFormRef" :model="planDayQueryParam" :inline="true">
                  <el-form-item label="会员名" prop="nickName">
                    <el-input v-model="planDayQueryParam.nickName" placeholder="请输入会员名" clearable/>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                  </el-form-item>
                  </el-form>
                </el-card>
              </div>
            </transition>

            <el-tabs v-model="activeTabPinyin">
                <el-tab-pane label="ABCD" name="ABCD">
                  <el-row class="el-row-magin">
                    <el-col :span="1">
                      <span>A</span>
                    </el-col>
                    <el-col :span="23">
                      <el-row v-if="membersPinyinMap && membersPinyinMap['A']" :gutter="10">
                        <el-col v-for="member in membersPinyinMap['A']??[]" :key="member.userId" :span="1.5">
                           <span @click="memberNameClick(member)" :class="['nickname']">{{ member.nickName }}</span>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>
                  <el-row class="el-row-magin">
                    <el-col :span="1">
                      <span>B</span>
                    </el-col>
                    <el-col :span="23">
                      <el-row v-if="membersPinyinMap && membersPinyinMap['B']" :gutter="10">
                        <el-col v-for="member in membersPinyinMap['B']??[]" :key="member.userId" :span="1.5">
                           <span @click="memberNameClick(member)" :class="['nickname']">{{ member.nickName }}</span>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>

                  <el-row class="el-row-magin">
                    <el-col :span="1">
                      <span>C</span>
                    </el-col>
                    <el-col :span="23">
                      <el-row v-if="membersPinyinMap && membersPinyinMap['C']" :gutter="10">
                        <el-col v-for="member in membersPinyinMap['C']??[]" :key="member.userId" :span="1.5">
                           <span @click="memberNameClick(member)" :class="['nickname']">{{ member.nickName }}</span>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>

                  <el-row class="el-row-magin">
                    <el-col :span="1">
                      <span>D</span>
                    </el-col>
                    <el-col :span="23">
                      <el-row v-if="membersPinyinMap && membersPinyinMap['D']" :gutter="10">
                        <el-col v-for="member in membersPinyinMap['D']??[]" :key="member.userId" :span="1.5">
                           <span @click="memberNameClick(member)" :class="['nickname']">{{ member.nickName }}</span>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>

                </el-tab-pane>
                <el-tab-pane label="FGHJ" name="FGHJ" >

                  <el-row class="el-row-magin">
                    <el-col :span="1">
                      <span>F</span>
                    </el-col>
                    <el-col :span="23">
                      <el-row v-if="membersPinyinMap && membersPinyinMap['F']" :gutter="10">
                        <el-col v-for="member in membersPinyinMap['F']??[]" :key="member.userId" :span="1.5">
                           <span @click="memberNameClick(member)" :class="['nickname']">{{ member.nickName }}</span>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>

                  <el-row  class="el-row-magin">
                    <el-col :span="1">
                      <span>G</span>
                    </el-col>
                    <el-col :span="23">
                      <el-row v-if="membersPinyinMap && membersPinyinMap['G']" :gutter="10">
                        <el-col v-for="member in membersPinyinMap['G']??[]" :key="member.userId" :span="1.5">
                           <span @click="memberNameClick(member)" :class="['nickname']">{{ member.nickName }}</span>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>

                  <el-row  class="el-row-magin">
                    <el-col :span="1">
                      <span>H</span>
                    </el-col>
                    <el-col :span="23">
                      <el-row v-if="membersPinyinMap && membersPinyinMap['H']" :gutter="10">
                        <el-col v-for="member in membersPinyinMap['H']??[]" :key="member.userId" :span="1.5">
                           <span @click="memberNameClick(member)" :class="['nickname']">{{ member.nickName }}</span>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>

                  <el-row  class="el-row-magin">
                    <el-col :span="1">
                      <span>J</span>
                    </el-col>
                    <el-col :span="23">
                      <el-row v-if="membersPinyinMap && membersPinyinMap['J']" :gutter="10">
                        <el-col v-for="member in membersPinyinMap['J']??[]" :key="member.userId" :span="1.5">
                           <span @click="memberNameClick(member)" :class="['nickname']">{{ member.nickName }}</span>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>
                </el-tab-pane>
                <el-tab-pane label="KLMN" name="KLMN">
                  <el-row class="el-row-magin">
                    <el-col :span="1">
                      <span>K</span>
                    </el-col>
                    <el-col :span="23">
                      <el-row v-if="membersPinyinMap && membersPinyinMap['K']" :gutter="10">
                        <el-col v-for="member in membersPinyinMap['K']??[]" :key="member.userId" :span="1.5">
                           <span @click="memberNameClick(member)" :class="['nickname']">{{ member.nickName }}</span>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>

                  <el-row class="el-row-magin">
                    <el-col :span="1">
                      <span>L</span>
                    </el-col>
                    <el-col :span="23">
                      <el-row v-if="membersPinyinMap && membersPinyinMap['L']" :gutter="10">
                        <el-col v-for="member in membersPinyinMap['L']??[]" :key="member.userId" :span="1.5">
                           <span @click="memberNameClick(member)" :class="['nickname']">{{ member.nickName }}</span>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>

                  <el-row class="el-row-magin">
                    <el-col :span="1">
                      <span>M</span>
                    </el-col>
                    <el-col :span="23">
                      <el-row v-if="membersPinyinMap && membersPinyinMap['M']" :gutter="10">
                        <el-col v-for="member in membersPinyinMap['M']??[]" :key="member.userId" :span="1.5">
                           <span @click="memberNameClick(member)" :class="['nickname']">{{ member.nickName }}</span>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>

                  <el-row class="el-row-magin">
                    <el-col :span="1">
                      <span>N</span>
                    </el-col>
                    <el-col :span="23">
                      <el-row v-if="membersPinyinMap && membersPinyinMap['N']" :gutter="10">
                        <el-col v-for="member in membersPinyinMap['N']??[]" :key="member.userId" :span="1.5">
                           <span @click="memberNameClick(member)" :class="['nickname']">{{ member.nickName }}</span>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>
                </el-tab-pane>

                <el-tab-pane label="PQRS" name="PQRS">
                  <el-row class="el-row-magin">
                    <el-col :span="1">
                      <span>P</span>
                    </el-col>
                    <el-col :span="23">
                      <el-row v-if="membersPinyinMap && membersPinyinMap['P']" :gutter="10">
                        <el-col v-for="member in membersPinyinMap['P']??[]" :key="member.userId" :span="1.5">
                           <span @click="memberNameClick(member)" :class="['nickname']">{{ member.nickName }}</span>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>

                  <el-row class="el-row-magin">
                    <el-col :span="1">
                      <span>Q</span>
                    </el-col>
                    <el-col :span="23">
                      <el-row v-if="membersPinyinMap && membersPinyinMap['Q']" :gutter="10">
                        <el-col v-for="member in membersPinyinMap['Q']??[]" :key="member.userId" :span="1.5">
                           <span @click="memberNameClick(member)" :class="['nickname']">{{ member.nickName }}</span>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>

                  <el-row class="el-row-magin">
                    <el-col :span="1">
                      <span>R</span>
                    </el-col>
                    <el-col :span="23">
                      <el-row v-if="membersPinyinMap && membersPinyinMap['R']" :gutter="10">
                        <el-col v-for="member in membersPinyinMap['R']??[]" :key="member.userId" :span="1.5">
                           <span @click="memberNameClick(member)" :class="['nickname']">{{ member.nickName }}</span>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>

                  <el-row class="el-row-magin">
                    <el-col :span="1">
                      <span>T</span>
                    </el-col>
                    <el-col :span="23">
                      <el-row v-if="membersPinyinMap && membersPinyinMap['S']" :gutter="10">
                        <el-col v-for="member in membersPinyinMap['S']??[]" :key="member.userId" :span="1.5">
                           <span @click="memberNameClick(member)" :class="['nickname']">{{ member.nickName }}</span>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>
                </el-tab-pane>


                <el-tab-pane label="TWXY" name="TWXY">
                  <el-row class="el-row-magin">
                    <el-col :span="1">
                      <span>T</span>
                    </el-col>
                    <el-col :span="23">
                      <el-row v-if="membersPinyinMap && membersPinyinMap['T']" :gutter="10">
                        <el-col v-for="member in membersPinyinMap['T']??[]" :key="member.userId" :span="1.5">
                           <span @click="memberNameClick(member)" :class="['nickname']">{{ member.nickName }}</span>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>
                  <el-row class="el-row-magin">
                    <el-col :span="1">
                      <span>W</span>
                    </el-col>
                    <el-col :span="23">
                      <el-row v-if="membersPinyinMap && membersPinyinMap['W']" :gutter="10">
                        <el-col v-for="member in membersPinyinMap['W']??[]" :key="member.userId" :span="1.5">
                           <span @click="memberNameClick(member)" :class="['nickname']">{{ member.nickName }}</span>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>

                  <el-row class="el-row-magin">
                    <el-col :span="1">
                      <span>X</span>
                    </el-col>
                    <el-col :span="23">
                      <el-row v-if="membersPinyinMap && membersPinyinMap['X']" :gutter="10">
                        <el-col v-for="member in membersPinyinMap['X']??[]" :key="member.userId" :span="1.5">
                           <span @click="memberNameClick(member)" :class="['nickname']">{{ member.nickName }}</span>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>

                  <el-row class="el-row-magin">
                    <el-col :span="1">
                      <span>S</span>
                    </el-col>
                    <el-col :span="23">
                      <el-row v-if="membersPinyinMap && membersPinyinMap['Y']" :gutter="10">
                        <el-col v-for="member in membersPinyinMap['Y']??[]" :key="member.userId" :span="1.5">
                           <span @click="memberNameClick(member)" :class="['nickname']">{{ member.nickName }}</span>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>
                </el-tab-pane>

                <el-tab-pane label="Z#" name="Z#">
                  <el-row class="el-row-magin">
                    <el-col :span="1">
                      <span>Z</span>
                    </el-col>
                    <el-col :span="23">
                      <el-row v-if="membersPinyinMap && membersPinyinMap['Z']" :gutter="10">
                        <el-col v-for="member in membersPinyinMap['Z']??[]" :key="member.userId" :span="1.5">
                           <span @click="memberNameClick(member)" :class="['nickname']">{{ member.nickName }}</span>
                        </el-col>
                      </el-row>
                    </el-col>
                  </el-row>
                </el-tab-pane>
              </el-tabs>
          </el-tab-pane>
          <el-tab-pane label="多人排课" name="multiple" >
            <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
              <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                  <el-form ref="queryFormRef" :model="planDayQueryParam" :inline="true">
                  <el-form-item label="年级" prop="nickName">
                    <el-input v-model="planDayQueryParam.nickName" placeholder="请选择年级" clearable/>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                  </el-form-item>
                  </el-form>
                </el-card>
              </div>
            </transition>
            <el-row>
              <el-col :span="20">
                <el-tabs v-model="activeTabPinyinMulti">
                  <el-tab-pane label="ABCD" name="ABCD">
                    <el-row class="el-row-magin">
                      <el-col :span="1">
                        <span>A</span>
                      </el-col>
                      <el-col :span="23">
                        <el-row v-if="membersPinyinMap && membersPinyinMap['A']" :gutter="10">
                          <el-col v-for="member in membersPinyinMap['A']??[]" :key="member.userId" :span="1.5">
                            <span @click="memberNameClick(member)" :class="['nickname', { active: mapUserActiveUserIds.has(member.userId)}]">{{ member.nickName }}</span>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>
                    <el-row class="el-row-magin">
                      <el-col :span="1">
                        <span>B</span>
                      </el-col>
                      <el-col :span="23">
                        <el-row v-if="membersPinyinMap && membersPinyinMap['B']" :gutter="10">
                          <el-col v-for="member in membersPinyinMap['B']??[]" :key="member.userId" :span="1.5">
                            <span @click="memberNameClick(member)" :class="['nickname', { active: mapUserActiveUserIds.has(member.userId)}]">{{ member.nickName }}</span>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>

                    <el-row class="el-row-magin">
                      <el-col :span="1">
                        <span>C</span>
                      </el-col>
                      <el-col :span="23">
                        <el-row v-if="membersPinyinMap && membersPinyinMap['C']" :gutter="10">
                          <el-col v-for="member in membersPinyinMap['C']??[]" :key="member.userId" :span="1.5">
                            <span @click="memberNameClick(member)" :class="['nickname', { active: mapUserActiveUserIds.has(member.userId)}]">{{ member.nickName }}</span>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>

                    <el-row class="el-row-magin">
                      <el-col :span="1">
                        <span>D</span>
                      </el-col>
                      <el-col :span="23">
                        <el-row v-if="membersPinyinMap && membersPinyinMap['D']" :gutter="10">
                          <el-col v-for="member in membersPinyinMap['D']??[]" :key="member.userId" :span="1.5">
                            <span @click="memberNameClick(member)" :class="['nickname', { active: mapUserActiveUserIds.has(member.userId)}]">{{ member.nickName }}</span>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>

                  </el-tab-pane>
                  <el-tab-pane label="FGHJ" name="FGHJ" >

                    <el-row class="el-row-magin">
                      <el-col :span="1">
                        <span>F</span>
                      </el-col>
                      <el-col :span="23">
                        <el-row v-if="membersPinyinMap && membersPinyinMap['F']" :gutter="10">
                          <el-col v-for="member in membersPinyinMap['F']??[]" :key="member.userId" :span="1.5">
                            <span @click="memberNameClick(member)" :class="['nickname', { active: mapUserActiveUserIds.has(member.userId)}]">{{ member.nickName }}</span>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>

                    <el-row  class="el-row-magin">
                      <el-col :span="1">
                        <span>G</span>
                      </el-col>
                      <el-col :span="23">
                        <el-row v-if="membersPinyinMap && membersPinyinMap['G']" :gutter="10">
                          <el-col v-for="member in membersPinyinMap['G']??[]" :key="member.userId" :span="1.5">
                            <span @click="memberNameClick(member)" :class="['nickname', { active: mapUserActiveUserIds.has(member.userId)}]">{{ member.nickName }}</span>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>

                    <el-row  class="el-row-magin">
                      <el-col :span="1">
                        <span>H</span>
                      </el-col>
                      <el-col :span="23">
                        <el-row v-if="membersPinyinMap && membersPinyinMap['H']" :gutter="10">
                          <el-col v-for="member in membersPinyinMap['H']??[]" :key="member.userId" :span="1.5">
                            <span @click="memberNameClick(member)" :class="['nickname', { active: mapUserActiveUserIds.has(member.userId)}]">{{ member.nickName }}</span>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>

                    <el-row  class="el-row-magin">
                      <el-col :span="1">
                        <span>J</span>
                      </el-col>
                      <el-col :span="23">
                        <el-row v-if="membersPinyinMap && membersPinyinMap['J']" :gutter="10">
                          <el-col v-for="member in membersPinyinMap['J']??[]" :key="member.userId" :span="1.5">
                            <span @click="memberNameClick(member)" :class="['nickname', { active: mapUserActiveUserIds.has(member.userId)}]">{{ member.nickName }}</span>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>
                  </el-tab-pane>
                  <el-tab-pane label="KLMN" name="KLMN">
                    <el-row class="el-row-magin">
                      <el-col :span="1">
                        <span>K</span>
                      </el-col>
                      <el-col :span="23">
                        <el-row v-if="membersPinyinMap && membersPinyinMap['K']" :gutter="10">
                          <el-col v-for="member in membersPinyinMap['K']??[]" :key="member.userId" :span="1.5">
                            <span @click="memberNameClick(member)" :class="['nickname', { active: mapUserActiveUserIds.has(member.userId)}]">{{ member.nickName }}</span>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>

                    <el-row class="el-row-magin">
                      <el-col :span="1">
                        <span>L</span>
                      </el-col>
                      <el-col :span="23">
                        <el-row v-if="membersPinyinMap && membersPinyinMap['L']" :gutter="10">
                          <el-col v-for="member in membersPinyinMap['L']??[]" :key="member.userId" :span="1.5">
                            <span @click="memberNameClick(member)" :class="['nickname', { active: mapUserActiveUserIds.has(member.userId)}]">{{ member.nickName }}</span>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>

                    <el-row class="el-row-magin">
                      <el-col :span="1">
                        <span>M</span>
                      </el-col>
                      <el-col :span="23">
                        <el-row v-if="membersPinyinMap && membersPinyinMap['M']" :gutter="10">
                          <el-col v-for="member in membersPinyinMap['M']??[]" :key="member.userId" :span="1.5">
                            <span @click="memberNameClick(member)" :class="['nickname', { active: mapUserActiveUserIds.has(member.userId)}]">{{ member.nickName }}</span>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>

                    <el-row class="el-row-magin">
                      <el-col :span="1">
                        <span>N</span>
                      </el-col>
                      <el-col :span="23">
                        <el-row v-if="membersPinyinMap && membersPinyinMap['N']" :gutter="10">
                          <el-col v-for="member in membersPinyinMap['N']??[]" :key="member.userId" :span="1.5">
                            <span @click="memberNameClick(member)" :class="['nickname', { active: mapUserActiveUserIds.has(member.userId)}]">{{ member.nickName }}</span>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>
                  </el-tab-pane>

                  <el-tab-pane label="PQRS" name="PQRS">
                    <el-row class="el-row-magin">
                      <el-col :span="1">
                        <span>P</span>
                      </el-col>
                      <el-col :span="23">
                        <el-row v-if="membersPinyinMap && membersPinyinMap['P']" :gutter="10">
                          <el-col v-for="member in membersPinyinMap['P']??[]" :key="member.userId" :span="1.5">
                            <span @click="memberNameClick(member)" :class="['nickname', { active: mapUserActiveUserIds.has(member.userId)}]">{{ member.nickName }}</span>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>

                    <el-row class="el-row-magin">
                      <el-col :span="1">
                        <span>Q</span>
                      </el-col>
                      <el-col :span="23">
                        <el-row v-if="membersPinyinMap && membersPinyinMap['Q']" :gutter="10">
                          <el-col v-for="member in membersPinyinMap['Q']??[]" :key="member.userId" :span="1.5">
                            <span @click="memberNameClick(member)" :class="['nickname', { active: mapUserActiveUserIds.has(member.userId)}]">{{ member.nickName }}</span>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>

                    <el-row class="el-row-magin">
                      <el-col :span="1">
                        <span>R</span>
                      </el-col>
                      <el-col :span="23">
                        <el-row v-if="membersPinyinMap && membersPinyinMap['R']" :gutter="10">
                          <el-col v-for="member in membersPinyinMap['R']??[]" :key="member.userId" :span="1.5">
                            <span @click="memberNameClick(member)" :class="['nickname', { active: mapUserActiveUserIds.has(member.userId)}]">{{ member.nickName }}</span>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>

                    <el-row class="el-row-magin">
                      <el-col :span="1">
                        <span>S</span>
                      </el-col>
                      <el-col :span="23">
                        <el-row v-if="membersPinyinMap && membersPinyinMap['S']" :gutter="10">
                          <el-col v-for="member in membersPinyinMap['S']??[]" :key="member.userId" :span="1.5">
                            <span @click="memberNameClick(member)" :class="['nickname', { active: mapUserActiveUserIds.has(member.userId)}]">{{ member.nickName }}</span>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>
                  </el-tab-pane>


                  <el-tab-pane label="TWXY" name="TWXY">
                    <el-row class="el-row-magin">
                      <el-col :span="1">
                        <span>T</span>
                      </el-col>
                      <el-col :span="23">
                        <el-row v-if="membersPinyinMap && membersPinyinMap['T']" :gutter="10">
                          <el-col v-for="member in membersPinyinMap['T']??[]" :key="member.userId" :span="1.5">
                            <span @click="memberNameClick(member)" :class="['nickname', { active: mapUserActiveUserIds.has(member.userId)}]">{{ member.nickName }}</span>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>
                    <el-row class="el-row-magin">
                      <el-col :span="1">
                        <span>W</span>
                      </el-col>
                      <el-col :span="23">
                        <el-row v-if="membersPinyinMap && membersPinyinMap['W']" :gutter="10">
                          <el-col v-for="member in membersPinyinMap['W']??[]" :key="member.userId" :span="1.5">
                            <span @click="memberNameClick(member)" :class="['nickname', { active: mapUserActiveUserIds.has(member.userId)}]">{{ member.nickName }}</span>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>

                    <el-row class="el-row-magin">
                      <el-col :span="1">
                        <span>X</span>
                      </el-col>
                      <el-col :span="23">
                        <el-row v-if="membersPinyinMap && membersPinyinMap['X']" :gutter="10">
                          <el-col v-for="member in membersPinyinMap['X']??[]" :key="member.userId" :span="1.5">
                            <span @click="memberNameClick(member)" :class="['nickname', { active: mapUserActiveUserIds.has(member.userId)}]">{{ member.nickName }}</span>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>

                    <el-row class="el-row-magin">
                      <el-col :span="1">
                        <span>Y</span>
                      </el-col>
                      <el-col :span="23">
                        <el-row v-if="membersPinyinMap && membersPinyinMap['Y']" :gutter="10">
                          <el-col v-for="member in membersPinyinMap['Y']??[]" :key="member.userId" :span="1.5">
                            <span @click="memberNameClick(member)" :class="['nickname', { active: mapUserActiveUserIds.has(member.userId)}]">{{ member.nickName }}</span>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>
                  </el-tab-pane>

                  <el-tab-pane label="Z#" name="Z#">
                    <el-row class="el-row-magin">
                      <el-col :span="1">
                        <span>Z</span>
                      </el-col>
                      <el-col :span="23">
                        <el-row v-if="membersPinyinMap && membersPinyinMap['Z']" :gutter="10">
                          <el-col v-for="member in membersPinyinMap['Z']??[]" :key="member.userId" :span="1.5">
                            <span @click="memberNameClick(member)" :class="['nickname', { active: mapUserActiveUserIds.has(member.userId)}]">{{ member.nickName }}</span>
                          </el-col>
                        </el-row>
                      </el-col>
                    </el-row>
                  </el-tab-pane>
                </el-tabs>
              </el-col>
              <el-col :span="4">
                <div style="margin-top: 30px;margin-left: 30px;">已选（{{mapUserActiveUserIds.size}}） <el-button type="primary" :disabled="mapUserActiveUserIds.size<1" @click="startStudyContent">下一步</el-button></div>
                <div style="margin-top: 10px;margin-left: 30px;">
                    <div v-if="mapUserActiveUsers" v-for="member in mapUserActiveUsers??[]">
                        <span> {{ member.nickName }}</span>
                    </div>
                </div>
              </el-col>
          </el-row>
        </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup name="StudyPlan" lang="ts">
import {listMembersPinyin} from '@/api/system/member';
import { MemberQueryPinyin,MemberVO} from '@/api/system/member/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const showSearch = ref(true);

const activeTab = ref('single');

const activeTabPinyin = ref('ABCD');

const activeTabPinyinMulti = ref('ABCD');

const queryFormRef = ref<ElFormInstance>();

const planDayQueryParam = ref<MemberQueryPinyin>({
  nickName: undefined,
});

const loading = ref(true);

const mapUserActiveUsers = ref<MemberVO[]>([]);
const mapUserActiveUserIds = ref<Set<number | string>>(new Set());
const memberNameClick = (member)=>{
  if(activeTab.value==='single'){
    startStudyContentSingle(member.userId);
    return;
  }
  if(mapUserActiveUserIds.value.has(member.userId)){
    mapUserActiveUserIds.value.delete(member.userId);
    //mapUserActiveUsers.value.remove(member);
    mapUserActiveUsers.value = mapUserActiveUsers.value.filter(memberVo => memberVo.userId !== member.userId);
  }else{
    mapUserActiveUserIds.value.add(member.userId);
    mapUserActiveUsers.value.push(member);
  }
}
/** 会员列表 */
const membersPinyinMap = ref<Map<string, MemberVO[]>>();

const getList = async () => {
  loading.value = true;
  const membersPinyin = await listMembersPinyin(planDayQueryParam.value);
  membersPinyinMap.value = membersPinyin.data;
  loading.value = false;
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
};
const startStudyContent = ()=>{
  proxy.$router.push({
    path: '/study/studyplan-content/index', // 对应路由配置的path
    query: { action: 'add',userIds:[...mapUserActiveUserIds.value].join(';')}, // 标识新增操作
  });
}
const startStudyContentSingle = (userIdParam)=>{
  proxy.$router.push({
    path: '/study/studyplan-content/index', // 对应路由配置的path
    query: { action: 'add',userIds:userIdParam}// 标识新增操作
  });
}

onMounted(() => {
  getList();
});
</script>
<style scoped>
.content {
  text-align: center; /* 文本居中 */
}

.el-row-magin {
  margin-bottom: 30px; /* 下边距 */
  margin-left: 30px;
}

.nickname {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

/* 默认样式 */
.nickname {
  color: #333;
  background-color: transparent;
}

/* 点击后的激活样式 */
.nickname.active {
  color: #fff;
  background-color: #409EFF; /* Element Plus 蓝色 */
}
</style>
