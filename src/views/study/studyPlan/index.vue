<template>
  <div class="p-2">
    <el-row :gutter="20">
      <el-col :lg="18" :xs="24">
          <!-- Navigation Tabs -->
          <div class="nav-tabs">
            <el-tabs v-model="activeTab">
              <el-tab-pane label="每日学习计划" name="dayplan">
                  <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
                    <div class="mb-[10px]">
                      <el-card shadow="hover">
                        <el-form ref="queryFormRef" :model="planDayQueryParam" :inline="true">
                          <el-form-item style="width: 150px">
                            <el-date-picker
                                v-model="planDayQueryParam.planDate"
                                type="date"
                                value-format="YYYY-MM-DD">
                            </el-date-picker>
                          </el-form-item>

                          <el-form-item label="伴学师" prop="studyMentorId" label-width="90">
                            <el-select v-model="planDayQueryParam.studyMentorId" placeholder="请选择伴学师" clearable style="width: 130px">
                              <el-option v-for="mentor in mentorList" :key="mentor.userId" :label="mentor.nickName"
                                        :value="mentor.userId"/>
                            </el-select>
                          </el-form-item>
                          <el-form-item label="会员姓名" prop="userName" label-width="90">
                            <el-input v-model="planDayQueryParam.userName" placeholder="请输入会员姓名" style="width: 120px"/>
                          </el-form-item>
                          <el-form-item>
                            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                          </el-form-item>
                        </el-form>
                      </el-card>
                    </div>
                  </transition>
                
                  <el-card>
                    <el-table :data="planDayList" border stripe :span-method="mergeCells">
                      <el-table-column prop="userId" label="会员Id"  width="-1" align="center" :formatter="hiddenFormatter"/>
                      <el-table-column prop="userName" label="会员" label-width="10%" align="center"/>
                      <el-table-column label="年级" align="center" prop="grade">
                        <template #default="scope">
                          <dict-tag v-if="scope.row.grade !== null && scope.row.grade !== undefined" :options="sys_member_grade" :value="scope.row.grade"/>
                        </template>
                      </el-table-column>
                      <el-table-column prop="studyMentorId" label="伴学师Id" width="-1" :formatter="hiddenFormatter"/>
                      <el-table-column prop="studyMentorNickName" label="伴学师" label-width="10%"/>
                      <el-table-column prop="attendanceStatus" label="到店和登录情况" label-width="10%"/>
                      <el-table-column label="学习时间" label-width="10%" :formatter="studyTimeFormatter"/>
                      <el-table-column  prop="studyContent" label="学习内容" label-width="10%"/>
                      <el-table-column label="视频时长" label-width="10%" :formatter="videoTimeFormatter"/>
                    </el-table>
                  </el-card>
              </el-tab-pane>
              <el-tab-pane label="本月学习计划" name="monthplan">
                <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
                    <div class="mb-[10px]">
                      <el-card shadow="hover">
                        <h3>
                        <el-date-picker
                          v-model="selectedMonthStr"
                          type="month"
                          format="YYYY-MM"
                          value-format="YYYY-MM"
                          placeholder="选择年月"
                          @change="generateCalendar"
                          style="width:120px;"
                        />
                        </h3>
                        <div class="calendar-wrapper">
                          <table>
                            <thead>
                              <tr>
                                <th v-for="day in daysOfWeek" :key="day">{{ day }}</th>
                              </tr>
                            </thead>
                            <tbody v-loading="loading">
                              <tr v-for="(week, weekIndex) in calendar" :key="weekIndex">
                                <td
                                  v-for="(day, dayIndex) in week"
                                  :key="dayIndex"
                                  :data-date="day?.date"
                                  :class="{
                                    'has-event': day?.event,
                                    'current-month': day?.isCurrentMonth,
                                  }"
                                >
                                 <span v-if="day">{{ day.day }}</span>
                                 <span v-for="(plan, planIndex) in day.plans" class="small-gray">{{ plan.planShowStr }}</span>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </el-card>
                    </div>
                </transition>
              </el-tab-pane>
            </el-tabs>
        </div>
      </el-col>

      <el-col :lg="5" style="margin-left: 10px;">
        <el-row style="height: 9vh;">
        </el-row>
        <el-row style="height: 6vh;" justify="left">
          <el-col :span="5">
            <el-button type="primary" @click="startCourseSchedule">开始排课</el-button>
          </el-col>
        </el-row>

        <el-row style="height: 17vh;" justify="center" align="middle">
          <el-col :span="24">
            <span>{{planDayQueryParam.planDate}}学习规划情况汇总</span>
          </el-col>
          <el-col :span="12">
            <div class="content"><span>计划学习</span></div>
            <div class="content">
              <span class="big-blue">{{resSummarMap.planCount}}人</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="content"><span>实际学习</span></div>
            <div class="content">
              <span class="big-blue">{{ resSummarMap.actualCount }} 人</span>
            </div>
          </el-col>
        </el-row>

        <el-row style="height: 17vh;" justify="center" align="middle">
          <el-col :span="24">
            <span>今日学习时长</span>
          </el-col>
           <el-col :span="12">
            <div class="content"><span>计划学习时长</span></div>
            <div class="content">
              <span class="big-blue" style="font-size: 15px;">{{ resSummarMap.planTime }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="content"><span>实际学习时长</span></div>
            <div class="content">
              <span class="big-blue" style="font-size: 15px;">{{ resSummarMap.actualTime }}</span>
            </div>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="StudyPlan" lang="ts">
import { queryListStudyPlan,queryPlanSummarize } from '@/api/study/studyPlan';
import { StudyPlanDayQuery,StudyPlanVO} from '@/api/study/studyPlan/types';
import {formatDateToStr} from '@/utils';
import {getMyDept} from '@/api/system/dept';
import {optionSelectByDeptId} from '@/api/system/user';
import { PlanDayQuery } from '@/api/study/studyPlan/types';
import { getPlanDayMonth } from '@/api/study/studyPlan';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const activeTab = ref('dayplan');

const { sys_member_grade} = toRefs<any>(proxy?.useDict('sys_member_grade'));

const planDayList = ref<StudyPlanVO[]>([]);

const queryFormRef = ref<ElFormInstance>();

const planDayQueryParam = ref<StudyPlanDayQuery>({
  planDate: formatDateToStr(new Date()),
  /**
   * 学校id
   */
  deptId: undefined,

  studyMentorId: undefined,

  userName: undefined,
} as StudyPlanDayQuery);

const loading = ref(true);

const valueMap = new Map();

const resSummarMap = ref<Map<string,object>>(new Map());
/** 计划列表 */
const getList = async () => {
  loading.value = true;
  if(planDayQueryParam.value && planDayQueryParam.value.planDate){
    const res = await queryListStudyPlan(planDayQueryParam.value);
    initMergeValueMap(res.data);
    planDayList.value = res.data;
  }
  loading.value = false;

  //总结
  const resSummarRes = await queryPlanSummarize(planDayQueryParam.value);
  resSummarMap.value = resSummarRes.data;
}
  // 合并单元格逻辑
  /**
   * if (columnIndex === 0) {
        // 合并 "会员" 列
        const userId = row.userId;
        const prevUserId = rowIndex > 0 ? planDayList.value[rowIndex - 1].userId : null;
        if (userId === prevUserId) {
          // 当前会员与上一行项目相同，隐藏当前单元格
          return [0, 0];
        } else {
          // 当前会员与上一行不同，计算合并行数
          const spanCount = planDayList.value.filter(item => item.userId === userId).length;
          return [spanCount, 1]; // 合并 `spanCount` 行，列合并为 1
        }
      }
      return [1, 1];
   * **/
  const getValueByRow = (row,columnIndex)=>{
    let resultvalue = ''
    if(row && row.userId && columnIndex>=0){
      resultvalue = resultvalue+row.userId
    }
    if(row && columnIndex>=1){
      resultvalue = resultvalue+"-";
    }
    if(row && row.userName && columnIndex>=1){
      resultvalue = resultvalue+row.userName;
    }
    if(row && columnIndex>=2){
      resultvalue = resultvalue+"-";
    }
    if(row && row.grade && columnIndex>=2){
      resultvalue = resultvalue+row.grade;
    }
    if(row && columnIndex>=3){
      resultvalue = resultvalue+"-";
    }
    if(row && row.studyMentorId && columnIndex>=3){
      resultvalue = resultvalue+row.studyMentorId;
    }
    if(row && columnIndex>=4){
      resultvalue = resultvalue+"-";
    }
    if(row && row.studyMentorName && columnIndex>=4){
      resultvalue = resultvalue+row.studyMentorName;
    }
    if(row && columnIndex>=5){
      resultvalue = resultvalue+"-";
    }
    if(row && row.attendanceStatus && columnIndex>=5){
      resultvalue = resultvalue+row.attendanceStatus;
    }
    return resultvalue;
  }
  /**
   * 
   *  // 合并 "会员" 列
        const userId = row.userId;
        const prevUserId = rowIndex > 0 ? planDayList.value[rowIndex - 1].userId : null;
        if (userId === prevUserId) {
          // 当前会员与上一行项目相同，隐藏当前单元格
          return [0, 0];
        } else {
          // 当前会员与上一行不同，计算合并行数
          const spanCount = planDayList.value.filter(item => item.userId === userId).length;
          return [spanCount, 1]; // 合并 `spanCount` 行，列合并为 1
        }
  */
 /**
  getList 
  planDayList.value = res.data; 赋值之前调用
  * 
 */
 
const selectedMonthStr = ref(formatDateToStr(new Date()).slice(0, 7));
const daysOfWeek = ref<string []>(["周一", "周二", "周三", "周四", "周五", "周六", "周日"]);

const calendar = ref([]);

const generateCalendar =  async () => {
  loading.value = true;
  let param:PlanDayQuery={
    yearMonth:selectedMonthStr.value
  }
  const result = await getPlanDayMonth(param);
  calendar.value = result.data;
  loading.value = false;
};
 const initMergeValueMap = (listData)=>{
  valueMap.clear();
  if(!listData){
    return;
  }
  listData.forEach((item, index) => {
    for (let i = 0; i < 6; i++) {
      let key = getValueByRow(item,i);
      if(!valueMap.get(key)){
        valueMap.set(key,0);
      }
      valueMap.set(key,valueMap.get(key)+1);
    }
  });
 }
  const mergeCells = ({ row, column, rowIndex, columnIndex }) => {
    if (columnIndex < 6) {
          // 合并 "会员" 列
        const key = getValueByRow(row,columnIndex);
        const prevKey = rowIndex > 0 ? getValueByRow(planDayList.value[rowIndex-1],columnIndex) : null;
        if (key === prevKey) {
          // 当前会员与上一行项目相同，隐藏当前单元格
          return [0, 0];
        } else {
          // 当前会员与上一行不同，计算合并行数
          const spanCount = valueMap.get(key);
          return [spanCount, 1]; // 合并 `spanCount` 行，列合并为 1
        }
      }
      return [1, 1];
  };
  const mentorList = ref([]); //
  const initDeptUserOption = async () => {
      const res = await getMyDept();
      const users = await optionSelectByDeptId(res.data.deptId);
      mentorList.value = users.data;
  };
  /** 搜索按钮操作 */
const handleQuery = () => {
  getList();
};
const studyTimeFormatter = (row, column, cellValue) => {
  let resultStr = '';
  if(row.startTime){
    const timePartStart = row.startTime.split(' ')[1]; // 得到 "10:00:00"
  // 提取时分
    const [hourStart, minuteStart] = timePartStart.split(':').slice(0, 2);
    resultStr = resultStr+hourStart+":"+minuteStart;
  }
  if(row.endTime){
    const timePartEnd = row.endTime.split(' ')[1]; // 得到 "10:00:00"
  // 提取时分
    const [hourEnd, minuteEnd] = timePartEnd.split(':').slice(0, 2);

    resultStr = resultStr + "-"+hourEnd+":"+minuteEnd;
  }
  return resultStr;
};
const videoTimeFormatter = (row, column, cellValue) => {
  let resultStr = '';
  if(row.videoMinute){
    resultStr = resultStr+row.videoMinute+"分";
  }
  if(row.videoSecond){
    resultStr = resultStr+row.videoSecond+"秒";
  }
  return resultStr;
};
const hiddenFormatter = (row, column, cellValue)=>{
  return null;
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};
const startCourseSchedule = ()=>{
  proxy.$router.push({
    path: '/study/studyplan-schedule/index', // 对应路由配置的path
    query: { action: 'add'}, // 标识新增操作
  });
}
onMounted(() => {
  getList();
  initDeptUserOption();
  generateCalendar();
});
</script>
<style scoped>
.content {
  text-align: center; /* 文本居中 */
}
.content {
  margin-left:10px;
  margin-top:10px;
  margin-bottom:10px;
  text-align: center;
}
.content-left {
  margin-left:5px;
  margin-top:10px;
  margin-bottom:10px;
  text-align: left;
}

.el-row-magin {
  margin-bottom: 10px; /* 下边距 */
  margin-left: 30px;
}
/* 全局覆盖 el-col 边框 */
.el-col-noborder {
  border: none !important;
}
.typename {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  color: #333;
  background-color: transparent;
}
/* 点击后的激活样式 */
.typename.active {
  color: #fff;
  background-color: #409EFF; /* Element Plus 蓝色 */
}
.small-gray {
  font-size: 12px;
  color: #666;
}

.container {
  display: flex;
  padding: 20px;
}
.course-list {
  width: 200px;
  margin-right: 20px;
}
.course-item {
  padding: 10px;
  margin: 5px 0;
  background-color: #f0f0f0;
  cursor: grab;
}
.calendar {
  flex: 1;
}
 
.table-container {
  overflow-x: auto;  /* 允许横向滚动 */
  max-width: 100%;   /* 自适应父容器宽度 */
  margin: 0 -20px;   /* 抵消父容器的padding */
  padding: 0 20px;   /* 添加内边距 */
}

table {
  width: 140%;
  border-collapse: collapse;
  min-width: 1000px;
  table-layout: fixed;
}

th,
td {
  border: 1px solid #ccc;
  text-align: center;
  padding: 10px;
  width: calc(100% / 7); /* 确保每列宽度相等，7天平均分配 */
  min-width: 120px;  /* 增加最小宽度 */
  white-space: nowrap; /* 防止文本换行 */
  vertical-align: top; /* 内容从顶部开始显示 */
}

td.current-month {
  background-color: #fff;
  height: 100px; /* 设置固定高度 */
}

td.has-event {
  background-color: #d1e7dd;
}

.event {
  display: block;
  font-size: 12px;
  color: #007bff;
}

/* 确保计划内容正确显示 */
.el-row {
  margin: 0 !important;
  width: 100%;
}

.el-col {
  padding: 0 !important;
  width: 100% !important;
}

/* 文本溢出显示省略号的核心样式 */
.ellipsis-text{
  display: inline-block; /* 或者 block，取决于布局需求 */
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 超出部分显示省略号 */
  /* 下面是可选的样式设置 */
  padding: 5px; /* 可选：增加内边距 */
  box-sizing: border-box; /* 确保padding不会影响总宽度 */
}
.cursor-point {
  cursor: pointer;
  font-size: 12px;
  color: #666;
}
.footer-buttons {
  position: fixed; /* 固定定位 */
  bottom: 0; /* 距离底部为 0 */
  left: 0;
  right: 0;
  background-color: #fff; /* 背景颜色，避免遮挡内容 */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
  padding: 10px 20px; /* 内边距 */
  z-index: 1000; /* 确保按钮层在最上层 */
}
.calendar-wrapper {
  width: 100%;
  overflow-x: auto;
  margin: 0 -20px;
  padding: 0 20px;
}

th,
td {
  border: 1px solid #ccc;
  text-align: center;
  width: calc(100% / 7);
  min-width: 120px;
  white-space: nowrap;
  vertical-align: top;
}

.plan-item {
  margin: 2px 0;
  padding: 2px 4px;
  background-color: #f5f7fa;
  border-radius: 2px;
}

.small-gray {
  font-size: 12px;
  color: #666;
  display: block;
  overflow: hidden;
}

td.current-month {
  background-color: #fff;
}

td.has-event {
  background-color: #d1e7dd;
}
.big-blue {
  font-size: 22px;
  font-weight: 500;
  color: #1890ff;
}
</style>
