<template>
  <div class="p-2">
    <el-row>
      <el-col :span="3">
        <div class="content"  v-loading="loading">班型</div>
        <div>
            <div class="content" v-if="classTypeList" v-for="classType in classTypeList??[]">
                <span @click="classNameClick(classType)" :class="['typename', { active: typeSelectedIds.has(classType.typeId)}]"> {{ classType.typeName }}</span>
            </div>
        </div>

      </el-col>
      <el-col :span="3" :offset="1"  v-loading="loading">
        <div class="content">科目</div>
        <div>
            <div class="content" v-if="subjectOptions" v-for="subject in subjectOptions??[]">
                <span @click="subjectNameClick(subject)" :class="['typename', { active: subjectSelectedIds.has(subject.subjectId)}]"> {{ subject.subjectName }}</span>
            </div>
        </div>
      </el-col>

      <el-col :span="6" :offset="1" v-loading="loading">
        <div class="content-left">课程名称</div>
        <div>
            <div class="content-left" v-if="courseVOList" v-for="course in courseVOList??[]">
                <el-button v-if="!course.open" icon="Download" @click="courseClick(course)"> {{ course.courseName }}</el-button>
                <el-button v-if="course.open" icon="Upload" @click="courseClick(course)"> {{ course.courseName }}</el-button>
                <el-select v-model="versionNameSelectedValue" placeholder="请选择版本" style="margin-left: 10px; width: 120px;">
                  <el-option v-for="item in versionNameSelected" :key="item" :label="item" :value="item" />
                </el-select>
                <div v-show="!course.open" style="height: 270px; overflow-y: auto;">
                  <template v-if="course.resourceVoList" v-for="resource in course.resourceVoList??[]">
                    <div class="content-left" style="margin-left:60px" v-if="resource.versionName==versionNameSelectedValue">
                        <span @click="resourceClick(resource)" :class="['typename', { active: resourceSelectedIds.has(resource.resourceId)}]"> {{ resource.videoResourceName }}</span>
                        <el-row>
                          <el-col :span="10" :offset="1" style="border: none !important;">
                            <span class="small-gray">科目:{{resource.subjectName}}</span>
                          </el-col>
                          <el-col :span="10" style="border: none !important;">
                            <span class="small-gray">版本:{{resource.versionName}}</span>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="10" :offset="1" style="border: none !important;">
                            <span class="small-gray">名师:{{resource.teacherName}}</span>
                          </el-col>
                        </el-row>
                    </div>
                  </template>
                </div>
            </div>
        </div>
      </el-col>

      <el-col :span="5" :offset="1">
        <div class="content-left">
          <span>已选课程</span>
          <span style="margin-left:20px">
            <el-button icon="Plus" :disabled="resourceSelectedIds.size<1" @click="gotoSchedule">下一步</el-button>
          </span>
        </div>

          <div>
            <div class="content-left" v-if="courseVOListSelected" v-for="course in courseVOListSelected??[]">
                <el-button v-if="course.open" icon="Download" @click="courseClick(course)"> {{ course.courseName }}</el-button>
                <el-button v-if="!course.open" icon="Upload" @click="courseClick(course)"> {{ course.courseName }}</el-button>
                <el-select v-if="course.open" v-model="versionNameSelectedValue" placeholder="请选择版本" style="margin-left: 10px; width: 120px;">
                  <el-option v-for="item in versionNameSelected" :key="item" :label="item" :value="item" />
                </el-select>
                <div v-show="!course.open" style="height: 270px; overflow-y: auto;">
                  <div class="content-left" style="margin-left:60px" v-if="course.resourceVoList" v-for="resource in course.resourceVoList??[]">
                      <span> {{ resource.videoResourceName }}</span>
                      <el-row>
                        <el-col :span="10" :offset="1" style="border: none !important;">
                          <span class="small-gray">科目:{{resource.subjectName}}</span>
                        </el-col>
                        <el-col :span="10" style="border: none !important;">
                          <span class="small-gray">版本:{{resource.versionName}}</span>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :span="10" :offset="1" style="border: none !important;">
                          <span class="small-gray">名师:{{resource.teacherName}}</span>
                        </el-col>
                      </el-row>
                  </div>
                </div>
            </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="StudyPlan" lang="ts">
import {optionSelectClassType} from '@/api/course/classType';
import { CourseVO } from '@/api/course/course/types';
import { CourseResourceQuerySchedule,CourseResourceVO } from '@/api/course/courseResource/types';
import { courseSchedule } from '@/api/course/courseResource/index';
import courseScheduleStore from '@/store/modules/courseSchedule';
import { CourseSubjectVO,CourseSubjectQuery } from '@/api/courseSubject/types';
import { queryListSubject} from '@/api/courseSubject';


const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const { sys_course_subject } = toRefs<any>(proxy?.useDict('sys_course_subject'));

const activeTab = ref('single');

const route = useRoute();

const loading = ref(false);

const classTypeList = ref([]); //班型选择
const initOptionClassType = async () => {
    const classTypeRes = await optionSelectClassType();
    classTypeList.value = classTypeRes.data;
};
const typeSelectedIds = ref<Set<number | string>>(new Set());
const classNameClick = (classType)=>{
  if(typeSelectedIds.value.has(classType.typeId)){
    typeSelectedIds.value.delete(classType.typeId);
  }else{
    typeSelectedIds.value.clear();
    typeSelectedIds.value.add(classType.typeId);
    courseAndResourceSchedule();
  }
}

const subjectSelectedIds = ref<Set<string>>(new Set());
const subjectNameClick = (subject)=>{
  if(subjectSelectedIds.value.has(subject.subjectId)){
    subjectSelectedIds.value.delete(subject.subjectId);
  }else{
    subjectSelectedIds.value.clear();
    subjectSelectedIds.value.add(subject.subjectId);
    courseAndResourceSchedule();
  }
}
// 方案 2：完整初始化（推荐）
const courseQueryForm = ref<CourseResourceQuerySchedule>({
  typeId: undefined,
  subjectId: undefined
});

const courseVOList = ref<CourseVO[]>([]);
const courseVOListSelected = ref<CourseVO[]>([]);
const versionNameSelected = ref<string[]>([]);
const versionNameSelectedValue = ref('');
const courseAndResourceSchedule = async ()=>{
  loading.value=true;
  courseVOList.value = [];
  if(typeSelectedIds.value.size==0 || subjectSelectedIds.value.size==0){
    loading.value=false;
    return;
  }
  const elementsType = Array.from(typeSelectedIds.value);
  courseQueryForm.value.typeId = elementsType[0];
  courseQueryForm.value.subjectId = subjectSelectedIds.value.values().next().value;
  const result = await courseSchedule(courseQueryForm.value);
  courseVOList.value = result.data;
  // 抽取 versionName 到 versionNameSelected
  versionNameSelected.value = [];
  versionNameSelectedValue.value='';
  if(courseVOList.value){
    versionNameSelected.value = Array.from(new Set(
      courseVOList.value.flatMap(course => course.resourceVoList.map(item => item.versionName))
    ));
    if(versionNameSelected.value && versionNameSelected.value.length>0){
      versionNameSelectedValue.value = versionNameSelected.value[0];
    }
  }
  console.log(versionNameSelected.value);
  loading.value=false;
}

const courseClick = (course)=>{
  if(!course.open){
    course.open = true
  }else{
    course.open = false
  }
}

const resourceSelectedIds = ref<Set<string|number>>(new Set());
const resourceClick = (resurce)=>{
    if(resourceSelectedIds.value.has(resurce.resourceId)){
      resourceSelectedIds.value.delete(resurce.resourceId);
    }else{
      resourceSelectedIds.value.add(resurce.resourceId);
    }
  // 过滤函数：返回符合条件的课程及其章节
  const filteredCourses = computed(() => {
  return courseVOList.value
    .map(course => {
      const matchedResources = course.resourceVoList.filter(resource =>
        resourceSelectedIds.value.has(resource.resourceId)
      );
      return matchedResources.length > 0 
        ? { ...course, resourceVoList: matchedResources }
        : null;
    })
    .filter(Boolean); // 等价于 course => course !== null
});
  const targetIndex = courseVOListSelected.value.findIndex(
    course => course.courseId === resurce.courseId
  );
  if(targetIndex<0){
    courseVOListSelected.value.push(filteredCourses.value[0]);
  }else{
    let resourceVoListTmp: CourseResourceVO[] = [];
    let listOld = courseVOListSelected.value[targetIndex].resourceVoList.filter(res => resourceSelectedIds.value.has(res.resourceId));
    let listNew = filteredCourses?.value[0]?.resourceVoList;

    let addedIds = new Set();
    
    if(listOld){
      for (const item of listOld) {
        if(!resourceSelectedIds.value.has(item.resourceId)){
          continue;
        }
        if(!addedIds.has(item.resourceId)){
          resourceVoListTmp.push(item);
        }
        addedIds.add(item.resourceId);
      }
    }
    if(listNew){
      for (const item of listNew) {
        if(!resourceSelectedIds.value.has(item.resourceId)){
          continue;
        }
        if(!addedIds.has(item.resourceId)){
          resourceVoListTmp.push(item);
        }
        addedIds.add(item.resourceId);
      }
    }
    courseVOListSelected.value[targetIndex].resourceVoList = resourceVoListTmp;
    if (!resourceVoListTmp || resourceVoListTmp.length === 0) {
      courseVOListSelected.value = courseVOListSelected.value.filter((_, index) => index !== targetIndex);
    }
    //console.log(courseVOListSelected.value.resourceVoList)
    //courseVOListSelected.value[targetIndex].resourceVoList.pushAll(filteredCourses.value[0].resourceVoList);
  }
  
}
const gotoSchedule=()=>{
  if(!courseVOListSelected.value){
    return;
  }
  courseScheduleStore().clearCourse();//清空缓存
  // 使用forEach遍历
  courseVOListSelected.value.forEach(course => {
    courseScheduleStore().addCourse(course);
  });
  proxy.$router.push({
    path: '/system/studyplan-schedule-date/index', // 对应路由配置的path
    query: { action: 'add',userIds:route.query.userIds,t: Date.now()}, // 标识新增操作
  });
}
const subjectOptions = ref<CourseSubjectVO[]>([]);
const initSubjectOptions= async () => {
  const query: CourseSubjectQuery = {
      pageNum: 1,
      pageSize: 10,
      subjectCode: undefined,
      subjectName: undefined,
      params: undefined
    };
    const rest = await queryListSubject(query);
    subjectOptions.value = rest.data;
}
onMounted(() => {
  courseScheduleStore().clearCourse();//清空缓存
  const userIds = route.query.userIds;
  let userIdArray: number[] = [];
  if (typeof userIds === 'string') {
    userIdArray = userIds
      .split(';')
      .map(item => Number(item))
      .filter(num => !isNaN(num));
  }
  initOptionClassType();
  initSubjectOptions();
});
</script>
<style scoped>
.content {
  margin-left:10px;
  margin-top:10px;
  margin-bottom:10px;
  text-align: center;
}
.content-left {
  margin-left:5px;
  margin-top:10px;
  margin-bottom:10px;
  text-align: left;
}

.el-row-magin {
  margin-bottom: 3px; /* 下边距 */
  margin-left: 30px;
}
.el-row {
  margin-bottom: 3px; 
}
/* 全局覆盖 el-col 边框 */
.el-col {
  border: 1px solid #e4e7ed !important;
}
.typename {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  color: #333;
  background-color: transparent;
}
/* 点击后的激活样式 */
.typename.active {
  color: #fff;
  background-color: #409EFF; /* Element Plus 蓝色 */
}
.small-gray {
    font-size: 12px;
    color: #666;
}
</style>
