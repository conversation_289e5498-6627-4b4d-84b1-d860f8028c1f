<template>
  <div class="p-2">
    <el-row :gutter="20">
      <!-- 左上：当前在读会员数 -->
      <el-col :span="6">
        <div class="stat-card" style="height: 300px; display: flex; flex-direction: column; justify-content: flex-start;">
          <div>
            <div class="stat-title">当前在读会员(人)</div>
            <div class="stat-value-row">
              <span class="stat-value">{{currentMemberCount}}</span>
              <a href="#" class="stat-link" style="display:none">查看</a>
            </div>
          </div>
          <div class="divider"></div>
          <div>
            <div class="season-title">当季在读会员(人)</div>
            <div class="season-value">{{ quarterMemberCount }}</div>
            <div class="season-desc">当前季度周期内，现在是正式会员或曾经是正式会员，购买了当季卡（含年卡、利用产品卡）；如果3天内没消费，将从在读人数中扣除；会员迁移后，统计在新门店，老门店去除。</div>
          </div>
        </div>
      </el-col>
      <!-- 右上：春续量续费率及折线图 -->
      <el-col :span="18">
        <div class="stat-card" style="height: 300px;">
          <div class="stat-title" style="font-weight:bold;">
            <span style="color: #f7b500;">春续量续费率</span>
            <span style="font-size:28px; margin-left: 20px;">{{ lastRenewal }}%</span>
            <span style="margin-left: 20px; color: #999; display:none;">健康参考值: 80%</span>
            <span style="margin-left: 20px; color: #999;">上季度在读会员人数: {{ lastNum }}, 已续费: {{ lastRenewalSum }}</span>
          </div>
          <div style="height: 230px; margin-top: 10px; background: #f5f7fa; border-radius: 8px; position: relative;">
            <div ref="lineChartRef" style="width: 100%; height: 100%;"></div>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20" class="mt-3">
      <!-- 左下：在读人数年级分布 -->
      <el-col :span="8">
        <div class="stat-card" style="height: 300px;">
          <div class="stat-title">在读人数年级分布</div>
          <div style="display: flex; align-items: center;">
            <div style="width: 140px; height: 140px; margin-right: 60px; position: relative;">
              <div ref="chartRef" style="width: 140px; height: 140px;"></div>
              <div class="circle-chart-center">
                <div class="circle-chart-label">总人数</div>
                <div class="circle-chart-value">{{currentMemberCount}}</div>
              </div>
            </div>
            <div class="grade-bg">
              <div class="grade-title">总分布</div>
              <div class="grade-list">
                <div class="grade-row" v-for="(item, index) in totalDistribution" :key="index">
                  <span class="grade-label">{{ item.grade }}</span>
                  <span class="grade-value">{{ item.sum }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <!-- 右下：伴学师续费率 -->
      <el-col :span="16">
        <div class="stat-card" style="height: 300px;">
          <div class="stat-title">伴学师续费率</div>
          <div class="teacher-list-scroll">
            <div v-for="(item, idx) in renewalRateData" :key="idx" class="teacher-row">
              <span :style="{color: idx < 3 ? ['#f56c6c','#f7b500','#409eff'][idx] : '#333', width: '90px', display: 'inline-block'}">{{ idx+1 }} {{ item.name }}</span>
              <el-progress :percentage="item.percent" :text-inside="true" :stroke-width="18" style="flex:1; margin-left: 18px;">
                <span class="teacher-value">{{ item.value }}</span>
              </el-progress>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="currentMember" lang="ts">
import { ref, onMounted, nextTick } from 'vue';
import * as echarts from 'echarts';
import { currenMemberStatic } from '@/api/statistic/currentMember';
import { CurrenMemberVo } from '@/api/statistic/currentMember/types';

const renewalRateData = ref([]);

const chartRef = ref(null);
const lineChartRef = ref(null);

let gradeData = [];

const currentMemberCount = ref(0);

const lastNum = ref(0);

const lastRenewal = ref(0);

const lastRenewalSum = ref(0);

const quarterMemberCount = ref(0);

let lineXAxisData = [];
let lineSeriesData = [];

let lineMemberSumData = [];

const totalDistribution = ref([]);
const getStatic = async () => {
  //在读会员统计
  const resSummarRes = await currenMemberStatic();
  totalDistribution.value = resSummarRes.data.totalDistribution;
  gradeData = resSummarRes.data.gradeDistribution;
}
onMounted(() => {
  nextTick(async () => {
    //在读会员统计
    const resSummarRes = await currenMemberStatic();
    totalDistribution.value = resSummarRes.data.totalDistribution;
    currentMemberCount.value = resSummarRes.data.currentMemberCount;
    quarterMemberCount.value = resSummarRes.data.quarterMemberCount;
    renewalRateData.value = resSummarRes.data.renewalRateData;
    gradeData = resSummarRes.data.gradeDistribution;
    lineXAxisData = resSummarRes.data.springToSummerTxt;
    lineSeriesData = resSummarRes.data.springToSummerPercent;
    lastNum.value = resSummarRes.data.lastNum;
    lastRenewalSum.value = resSummarRes.data.lastRenewalSum;
    lastRenewal.value = resSummarRes.data.lastRenewal;
    lineMemberSumData = resSummarRes.data.springToSummerSum;
    // 饼图
    if (chartRef.value) {
      const myChart = echarts.init(chartRef.value);
      myChart.setOption({
        tooltip: { trigger: 'item' },
        legend: { show: false },
        series: [
          {
            name: '年级分布',
            type: 'pie',
            radius: ['65%', '90%'],
            avoidLabelOverlap: false,
            label: { show: false },
            data: gradeData,
            color: ['#5B8FF9', '#61DDAA', '#F6BD16', '#E8684A']
          }
        ]
      });
    }
    // 折线图
    if (lineChartRef.value) {
      const lineChart = echarts.init(lineChartRef.value);
      lineChart.setOption({
        tooltip: { 
          trigger: 'axis',
          formatter: function(params) {
            const dataIndex = params[0].dataIndex;
            return `${params[0].name}<br/>续费率: ${params[0].value}%<br/>数量: ${lineMemberSumData[dataIndex]}`;
          }
        },
        grid: { left: 50, right: 30, top: 40, bottom: 30 },
        xAxis: {
          type: 'category',
          data: lineXAxisData,
          axisLabel: {
            fontSize: 14,
            color: '#333'
          },
          axisLine: { lineStyle: { color: '#e5e6eb' } }
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 100,
          axisLabel: {
            formatter: '{value} %',
            color: '#888',
            fontSize: 13
          },
          splitLine: { lineStyle: { color: '#f0f1f2' } }
        },
        series: [
          {
            name: '续费率',
            type: 'line',
            data: lineSeriesData,
            smooth: true,
            symbol: 'circle',
            symbolSize: 8,
            lineStyle: { width: 3, color: '#409eff' },
            itemStyle: { color: '#409eff' }
          }
        ]
      });
    }
  });
});
</script>

<style scoped>
.stat-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px #f0f1f2;
  padding: 20px;
  margin-bottom: 10px;
}
.stat-title {
  font-size: 16px;
  color: #333;
  margin-bottom: 10px;
}
.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #222;
  margin-bottom: 8px;
}
.stat-desc {
  font-size: 12px;
  color: #888;
  margin-top: 8px;
}
.mt-2 {
  margin-top: 12px;
}
.mt-3 {
  margin-top: 24px;
}
.stat-value-row {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}
.stat-link {
  font-size: 14px;
  color: #409eff;
  text-decoration: underline;
  margin-left: 8px;
  margin-bottom: 4px;
  transition: color 0.2s;
}
.stat-link:hover {
  color: #1867c0;
}
.grade-bg {
  background: #e9eef6;
  border-radius: 8px;
  padding: 16px 20px;
  min-width: 120px;
}
.grade-title {
  font-size: 16px;
  font-weight: 600;
  color: #222;
  margin-bottom: 10px;
}
.grade-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.grade-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 15px;
  color: #333;
  line-height: 1.7;
  background: #f5f7fa;
  border-radius: 6px;
  padding: 2px 8px;
}
.grade-label {
  font-weight: 600;
  font-size: 15px;
  color: #333;
}
.grade-value {
  font-weight: 400;
  font-size: 15px;
  color: #888;
}
.grade-row.total .grade-label {
  color: #409eff;
}
.grade-row.total .grade-value {
  color: #409eff;
  font-weight: bold;
}
.circle-chart-center {
  position: absolute;
  left: 0;
  top: 0;
  width: 140px;
  height: 140px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}
.circle-chart-label {
  font-size: 14px;
  color: #888;
}
.circle-chart-value {
  font-size: 28px;
  font-weight: bold;
  color: #409eff;
  line-height: 1.2;
}
.season-title {
  font-size: 14px;
  color: #888;
  font-weight: 500;
  margin-bottom: 2px;
}
.season-value {
  font-size: 14px;
  color: #0e0c0c;
  font-weight: 500;
  margin: 8px 0 4px 0;
  letter-spacing: 0;
  line-height: 1.1;
}
.season-desc {
  font-size: 12px;
  color: #bbb;
  margin-top: 4px;
  line-height: 1.6;
}
/* 伴学师续费率进度条百分比样式 */
.el-progress-bar__innerText {
  font-size: 13px !important;
  color: #fff !important;
  font-weight: bold;
  text-align: center;
  line-height: 18px !important;
  padding: 0 4px;
}
.el-progress-bar__inner {
  background-color: #193b16 !important;
}
.teacher-list-scroll {
  max-height: 220px;
  overflow-y: auto;
  margin-top: 10px;
  /* 保证背景和圆角继承自父容器 */
}
.teacher-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.teacher-value {
  width: 80px;
  text-align: right;
  margin-left: 16px;
  font-size: 13px;
  color: #888;
  line-height: 18px;
  display: inline-block;
  vertical-align: middle;
  white-space: nowrap;
}
.divider {
  height: 1px;
  background: #f0f1f2;
  margin: 18px 0 14px 0;
  border: none;
}
</style>
