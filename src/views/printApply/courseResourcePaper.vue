<template>
  <div class="p-2">
    <el-row justify="center" v-for="(question, index) in paperList" :key="question.paperId" style="margin-bottom: 20px;">
      <!-- 题目 -->
      <el-col :span="21" :offset="0">
        <div style="overflow: hidden;margin-bottom: 20px;">
          <div style="font-size: 1em;float: left;">{{ index + 1 }}、 </div>
          <div style="font-size: 1em;float: left;" v-html="renderFormula(question.content)"></div>
        </div>
      </el-col>
      <!-- 选项 -->
      <el-col :span="21" justify="center" :offset="4">
        <el-row v-if="question.optionVoList" v-for="(option, opIndex) in question.optionVoList" :key="index" style="margin-bottom: 10px;">
          <span v-if="question.cate==1" v-html="indexToLetter(opIndex)"></span>
          <span v-if="question.cate!=1">{{opIndex}} 、</span>
          <span v-html="renderFormula(option.paperOption)"></span>
        </el-row>
        <el-row v-if="!question.optionVoList" style="margin-bottom: 20%;">
        </el-row>
      </el-col>
    <!-- 答案 -->
      <el-col :span="21" justify="center" :offset="3">
        <el-row style="margin-bottom: 10px;">
          <span>答案：</span>
          <span v-if="question.cate==1" v-html="indexToLetterAn(question.answers)"></span>
          <span v-if="question.cate!=1" v-html="renderFormula(question.answers)" ></span>
        </el-row>
      </el-col>
          <!-- 解析 -->
      <el-col :span="21" justify="center" :offset="3">
        <el-row style="margin-bottom: 10px;">
          <span>解析：</span>
          <span v-html="renderFormula(question.analyse)" ></span>
        </el-row>
      </el-col>

      <!-- 解答 -->
      <el-col :span="21" justify="center" :offset="3" v-if="question.method">
        <el-row style="margin-bottom: 10px;">
          <span>解答：</span>
          <span v-html="renderFormula(question.method)" ></span>
        </el-row>
      </el-col>
      <!-- 解答 -->
      <el-col :span="21" justify="center" :offset="3" v-if="question.discuss">
        <el-row style="margin-bottom: 10px;">
          <span>点评：</span>
          <span v-html="renderFormula(question.discuss)" ></span>
        </el-row>
      </el-col>
    </el-row>
    <el-row justify="end" class="no-print">
      <el-col :span="15" :offset="3">
        <div class="dialog-footer">
          <el-button type="primary" @click="printPaper">打印</el-button>
      </div>  
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="PaperPrint" lang="ts">
import { queryByResourceId } from '@/api/jyeoo/jyeooPaper';
import { JyeooPaperVO} from '@/api/jyeoo/jyeooPaper/types';
import { ref, onMounted } from 'vue'
import katex from 'katex'
import 'katex/dist/katex.min.css'


const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const route = useRoute();

const paperList = ref<JyeooPaperVO[]>([]);


/** 查询试题 */
const getPaperList = async () => {
  const res = await queryByResourceId(String(route.query.resourceId));
  paperList.value = res.data;

}
const renderFormula = (str) => {
  return str.replace(/\$(.*?)\$/g, (_, formula) => {
    return katex.renderToString(formula, { throwOnError: false })
  })
}
const indexToLetter = (index) => {
  return String.fromCharCode(65 + Number(index))+"、" // 65对应ASCII码'A'
}
const indexToLetterAn = (indexStr) => {
  return [...indexStr]
    .map(ch => String.fromCharCode(65 + parseInt(ch, 10)) + " ")
    .join('');
}
const printPaper = () => {
 window.print(); // 确保DOM更新完成后再打印
}
onMounted(async () => {
  await getPaperList(); // 确保数据加载完成
});
</script>
<style scoped>
.math-content {
  font-size: 1.2em;
  margin: 20px 0;
}

/* 新增：打印时不显示指定的元素 */
@media print {
  .no-print {
    display: none !important;
  }
}
</style>
