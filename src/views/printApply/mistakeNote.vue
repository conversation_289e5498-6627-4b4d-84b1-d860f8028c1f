<template>
  <div class="p-2" v-loading="loading" >
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="userIdSelect" :inline="true">
             <el-form-item>
              <el-select v-model="userIdSelect" placeholder="请选择会员" filterable style="width: 130px">
                <el-option v-for="member in memberList" :key="member.userId" :label="member.nickName" :value="member.userId"/>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="getPaperList">搜索</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>
   <el-tabs v-model="activeTabMis"  class="custom-tab-pane">
      <el-tab-pane
        v-for="subject in subjectList"
        :key="subject.subjectId"
        :label="subject.subjectName"
        :name="subject.subjectId"
      >
       <el-empty v-if="paperList.length === 0" description="暂无数据" />
       <el-row v-else justify="center" v-for="(question, index) in paperList" :key="question.paperId" style="margin-bottom: 20px;">
        <!-- 题目 -->
        <el-col :span="21" :offset="0">
          <div style="overflow: hidden;margin-bottom: 20px;">
            <el-checkbox 
              v-model="selectedNotes" 
              :value="question.noteId"
              style="float: left; margin-right: 10px;"
            />
            <div style="font-size: 1em;float: left;">{{ index + 1 }}、 </div>
            <div style="font-size: 1em;float: left;" v-html="renderFormula(question.content)"></div>
            <el-button 
              type="danger" 
              size="small" 
              style="float: right;"
              @click="handleDelete(question.noteId)"
            >
              删除
            </el-button>
          </div>
        </el-col>
      </el-row>
      <!-- 打印按钮 -->
      <div v-if="paperList.length > 0" class="print-button-container">
        <el-button type="primary" @click="handlePrint">去打印</el-button>
      </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup name="MistakeNote" lang="ts">
import { MistakeNoteVO} from '@/api/jyeoo/mistakeNote/types';
import {delMistakeNote} from '@/api/jyeoo/mistakeNote';
import { mistakeQueryByUserId,optionMember} from '@/api/system/member';
import { CourseSubjectVO,CourseSubjectQuery } from '@/api/courseSubject/types';
import { queryListSubject} from '@/api/courseSubject';
import { useRouter } from 'vue-router';

import katex from 'katex';
import 'katex/dist/katex.min.css';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const router = useRouter();

const activeTabMis = ref();

const loading = ref(false);

const paperList = ref<MistakeNoteVO[]>([]);

const props = defineProps({
  param: { type: null, required: true }
});
const userId = computed(() => props.param);
const showSearch = ref(false);
const userIdSelect = ref();
/** 查询试题 */
const getPaperList = async () => {
  if((!userId || !userId.value) && !showSearch.value){
    optionMemberQuery();
    showSearch.value = true;
  }
  let userIdQuery = null;
  if(userIdSelect && userIdSelect.value){
    userIdQuery = userIdSelect.value;
  }
  if(userId && userId.value){
    userIdQuery = userId.value;
  }
  if(!userIdQuery){
    return;
  }
  loading.value = true;
  const res = await mistakeQueryByUserId(userIdQuery,activeTabMis.value);
  paperList.value = res.data;
  console.log('getPaperList params:', {
    userId: userIdQuery,
    subjectId: activeTabMis.value,
    paperList: res.data
  });
  loading.value = false;
};
const renderFormula = (str) => {
  return str.replace(/\$(.*?)\$/g, (_, formula) => {
    return katex.renderToString(formula, { throwOnError: false })
  })
};
const memberList = ref([]);
const optionMemberQuery = async()=>{
    const rest = await optionMember();
    memberList.value = rest.data;
};


const subjectList = ref<CourseSubjectVO[]>([]);
const querySubject = async()=>{
    const query: CourseSubjectQuery = {
      pageNum: 1,
      pageSize: 10,
      subjectCode: undefined,
      subjectName: undefined,
      params: undefined
    };
    const rest = await queryListSubject(query);
    subjectList.value = [
      { subjectId: 0, subjectName: '全部' },
      ...rest.data
    ];
    activeTabMis.value = subjectList.value[1].subjectId;
}
// 监听 activeTabMis 的变化
watch(
  () => activeTabMis.value,
  (newVal, oldVal) => {
        getPaperList();
  },
  { immediate: false } // 如果初始值需要处理，设置为 true
);
onMounted(() => {
  querySubject();
});

const handleDelete = async (noteId: string) => {
  try {
    await proxy.$modal.confirm('是否确认删除该错题？');
    await delMistakeNote(noteId);
    proxy.$modal.msgSuccess('删除成功');
    getPaperList(); // 重新加载列表
  } catch (error) {
    console.error('删除失败:', error);
  }
};

// 选中的错题ID列表
const selectedNotes = ref<(string | number)[]>([]);

// 处理打印
const handlePrint = () => {
  if (selectedNotes.value.length === 0) {
    proxy.$modal.msgWarning('请选择要打印的错题');
    return;
  }
  console.log('选中的错题IDs:', selectedNotes.value);
  localStorage.removeItem('mistakeNotePrintIds');
  localStorage.setItem('mistakeNotePrintIds', JSON.stringify(selectedNotes.value));
  const fullPath = proxy.$router.resolve({
    path: '/print-apply/mistake-note-print',
  }).href;
  window.open(fullPath, '_blank');
};
</script>
<style scoped>
.math-content {
  font-size: 1.2em;
  margin: 20px 0;
}
.custom-tab-pane {
  background-color: #e9f1ff;
  color: #060606;
  padding: 20px;
}
.print-button-container {
  text-align: center;
  margin-top: 20px;
  padding: 20px;
}
</style>
