<template>
  <div class="p-2">
    <el-row justify="center" style="margin-bottom: 10px;">
      <el-col :span="10" :offset="3">
        <span v-if="applyInfo" :style="{  fontSize: '1em' }">{{applyInfo.nickName}}({{applyInfo.userName}})</span>
      </el-col>
    </el-row>
    <div style="width:100wv ;height: 1px;background-color: rgba(0,0,0,0.1);margin:2px 0;"></div>
    <div style="width:100wv ;height: 1px;background-color: rgba(0,0,0,0.1);margin:2px 0; margin-bottom: 20px;"></div>
    <el-row justify="end" style="display:none;">
      <el-col :span="10">
        <img :src="qrcodeDataURL" alt="二维码" />
      </el-col>
    </el-row>
    <el-row justify="center" v-for="(question, index) in paperList" :key="question.paperId" style="margin-bottom: 20px;">
      <!-- 题目 -->
      <el-col :span="21" :offset="0">
        <div style="overflow: hidden;margin-bottom: 20px;">
          <div style="font-size: 1em;float: left;">{{ index + 1 }}、 </div>
          <div style="font-size: 1em;float: left;" v-html="renderFormula(question.content)"></div>
        </div>
      </el-col>

      <!-- 选项 -->
      <el-col :span="21" justify="center" :offset="4">
        <el-row v-if="question.optionVoList" v-for="(option, opIndex) in question.optionVoList" :key="index" style="margin-bottom: 10px;">
          <span v-if="question.cate==1" v-html="indexToLetter(opIndex)"></span>
          <span v-if="question.cate!=1">{{opIndex}} 、</span>
          <span v-html="renderFormula(option.paperOption)"></span>
        </el-row>
        <el-row v-if="!question.optionVoList" style="margin-bottom: 20%;">
        </el-row>
      </el-col>
    <!-- 答案 -->
      <el-col :span="21" justify="center" :offset="3">
        <el-row v-if="printAnswers" style="margin-bottom: 10px;">
          <span>答案：</span>
          <span v-if="question.cate==1" v-html="indexToLetterAn(question.answers)"></span>
          <span v-if="question.cate!=1" v-html="renderFormula(question.answers)" ></span>
        </el-row>
      </el-col>
          <!-- 解析 -->
      <el-col :span="21" justify="center" :offset="3">
        <el-row v-if="printAnswers && question.analyse" style="margin-bottom: 10px;">
          <span>解析：</span>
          <span v-html="renderFormula(question.analyse)" ></span>
        </el-row>
      </el-col>
      <!-- 解答 -->
      <el-col :span="21" justify="center" :offset="3" v-if="printAnswers && question.method">
        <el-row style="margin-bottom: 10px;">
          <span>解答：</span>
          <span v-html="renderFormula(question.method)" ></span>
        </el-row>
      </el-col>
      <!-- 解答 -->
      <el-col :span="21" justify="center" :offset="3" v-if="printAnswers && question.discuss">
        <el-row style="margin-bottom: 10px;">
          <span>点评：</span>
          <span v-html="renderFormula(question.discuss)" ></span>
        </el-row>
      </el-col>


    </el-row>
    <el-row justify="end" class="no-print">
      <el-col :span="15" :offset="3">
        <div class="dialog-footer">
          <el-button type="primary" @click="printPaper">打印</el-button>
      </div>  
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="PaperPrint" lang="ts">
import { queryByApplyId,getbyMistakeIds } from '@/api/jyeoo/jyeooPaper';
import { JyeooPaperVO} from '@/api/jyeoo/jyeooPaper/types';
import { ref, reactive, onMounted } from 'vue';
import { updatePrintStatus,getPrintApply } from '@/api/printApply';
import { PrintApplyForm,PrintApplyVO } from '@/api/printApply/types';
import katex from 'katex';
import 'katex/dist/katex.min.css';
import QRCode from 'qrcode';
import { MistakeNoteVO } from '@/api/jyeoo/mistakeNote/types';


const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const route = useRoute();

const paperList = ref<MistakeNoteVO[]>([]);

const printAnswers = ref<boolean>(false);

const applyInfo = ref<PrintApplyVO>(null);

/** 查询试题 */
const getPaperList = async () => {
  const ids = JSON.parse(localStorage.getItem('mistakeNotePrintIds'));
  localStorage.removeItem('mistakeNotePrintIds');
  console.log(ids.join(';'))
  const res = await getbyMistakeIds(ids.join(';'));
  paperList.value = res.data;
  if(paperList.value){
    const applyInfoRes = await getPrintApply(paperList.value[0].applyId);
    applyInfo.value = applyInfoRes.data;
  }
}
const qrcodeDataURL = ref('');
const qrcodeGen = async () => {
  const text = `http://${window.location.hostname}/print-apply/correct-paper?applyId=${route.query.applyId}`
  const width = 100;
  const height = 100;
  const correctLevel = 'M'; // 可选纠错等级：L/M/Q/H
  try {
    const url = await QRCode.toDataURL(text, {
      width,
      height,
      errorCorrectionLevel: correctLevel
    });

    qrcodeDataURL.value = url;
    console.log('生成成功:', url);
  } catch (error) {
    console.error('生成二维码失败:', error);
  }
}

const renderFormula = (str) => {
  return str.replace(/\$(.*?)\$/g, (_, formula) => {
    return katex.renderToString(formula, { throwOnError: false })
  })
}
const printPaper = () => {
 window.print(); // 确保DOM更新完成后再打印
}
const indexToLetter = (index) => {
  return String.fromCharCode(65 + Number(index))+"、" // 65对应ASCII码'A'
}
const indexToLetterAn = (indexStr) => {
  return [...indexStr]
    .map(ch => String.fromCharCode(65 + parseInt(ch, 10)) + " ")
    .join('');
}

onMounted(async () => {
  await getPaperList(); // 确保数据加载完成
  //await qrcodeGen();
});
</script>
<style scoped>
.math-content {
  font-size: 1.2em;
  margin: 20px 0;
}

/* 新增：打印时不显示指定的元素 */
@media print {
  .no-print {
    display: none !important;
  }
}
</style>
