<template>
  <div class="print-body">
    <div class="print-container">
      <div class="print-header">
        <div class="header-left">
          <img src="@/assets/logo/print-logo.png" class="header-logo" />
        </div>
        <div class="header-right no-print">生成日期：{{ currentDate }}</div>
      </div>
      <div v-if="applyInfo" class="print-title">{{ applyInfo.nickName }}({{ applyInfo.userName }})</div>
      <img class="code-img" v-if="qrcodeDataURL" :src="qrcodeDataURL" alt="二维码" />
      <ul class="question-list">
        <li v-for="(question, index) in paperList" :key="question.paperId">
          <!-- 题目 -->
          <div class="question">
            <span class="question-no">{{ index + 1 }}.</span>
            <div v-html="renderFormula(question.content)"></div>
          </div>
          <!-- 选项 -->
          <div class="option-list">
            <template v-if="question.optionVoList">
              <template v-if="isShortOptions(question.optionVoList)">
                <template v-for="(group, groupIdx) in groupOptions(question.optionVoList, 2)" :key="groupIdx">
                  <div v-for="(option, opIndex) in group" :key="opIndex" class="option-item">
                    <span class="question-no" v-if="question.cate == 1" v-html="indexToLetter(groupIdx * 2 + opIndex)"></span>
                    <div class="question-no" v-if="question.cate != 1">{{ groupIdx * 2 + opIndex }}.</div>
                    <div v-html="renderFormula(option.paperOption)"></div>
                  </div>
                </template>
              </template>
              <template v-else>
                <div v-for="(option, opIndex) in question.optionVoList" :key="opIndex" class="option-item">
                  <span class="question-no" v-if="question.cate == 1" v-html="indexToLetter(opIndex)"></span>
                  <div class="question-no" v-if="question.cate != 1">{{ opIndex }}.</div>
                  <div v-html="renderFormula(option.paperOption)"></div>
                </div>
              </template>
            </template>
          </div>
          <!-- 答案 -->
          <div v-if="printAnswers" class="answer-section">
            <div class="answer">
              <span class="answer-label">答案:</span>
              <div class="flex items-center">
                <span>【</span>
                <span v-if="question.cate == 1" v-html="question.cate == 1 ? indexToLetterAn(question.answers) : renderFormula(question.answers)">
                </span>
                <span>】</span>
              </div>
            </div>
            <!-- 解析 -->
            <div v-if="question.analyse" class="answer">
              <span class="answer-label">解析：</span>
              <div v-html="renderFormula(question.analyse)"></div>
            </div>
            <!-- 解答 -->
            <div v-if="question.method" class="answer">
              <span class="answer-label">解答：</span>
              <div v-html="renderFormula(question.method)"></div>
            </div>
            <!-- 解答 -->
            <div v-if="question.discuss" class="answer">
              <span class="answer-label">点评：</span>
              <div v-html="renderFormula(question.discuss)"></div>
            </div>
          </div>
        </li>
      </ul>
      <div class="print-button no-print">
        <el-button type="primary" @click="printPaper">打印</el-button>
      </div>
      <div>
        <p v-for="i in 100" :key="i">这是内容 - 第{{ i }}页</p>
      </div>
    </div>
  </div>
</template>

<script setup name="PaperPrint" lang="ts">
import { queryByApplyId } from '@/api/jyeoo/jyeooPaper';
import { JyeooPaperVO } from '@/api/jyeoo/jyeooPaper/types';
import { ref, reactive, onMounted } from 'vue';
import { updatePrintStatus, getPrintApply } from '@/api/printApply';
import { PrintApplyForm, PrintApplyVO } from '@/api/printApply/types';
import katex from 'katex';
import 'katex/dist/katex.min.css';
import QRCode from 'qrcode';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const route = useRoute();

const paperList = ref<JyeooPaperVO[]>([]);

const printAnswers = ref<boolean>(false);

const applyInfo = ref<PrintApplyVO>(null);

const currentDate = ref('');
const pad = (n) => n.toString().padStart(2, '0');
const updateDate = () => {
  const d = new Date();
  currentDate.value = `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}`;
};
updateDate();

/** 查询试题 */
const getPaperList = async () => {
  const res = await queryByApplyId(String(route.query.applyId));
  paperList.value = res.data;
  if (route.query.answers) {
    printAnswers.value = true;
  }
  const applyInfoRes = await getPrintApply(String(route.query.applyId));
  applyInfo.value = applyInfoRes.data;
};
const qrcodeDataURL = ref('');
const qrcodeGen = async () => {
  const text = `http://${window.location.hostname}/print-apply/correct-paper?applyId=${route.query.applyId}`;
  const width = 100;
  const height = 100;
  const correctLevel = 'M'; // 可选纠错等级：L/M/Q/H
  try {
    const url = await QRCode.toDataURL(text, {
      width,
      height,
      errorCorrectionLevel: correctLevel
    });

    qrcodeDataURL.value = url;
    console.log('生成成功:', url);
  } catch (error) {
    console.error('生成二维码失败:', error);
  }
};

const renderFormula = (str) => {
  return str.replace(/\$(.*?)\$/g, (_, formula) => {
    return katex.renderToString(formula, { throwOnError: false });
  });
};
const printPaper = () => {
  window.print(); // 确保DOM更新完成后再打印
  let formCorrect: PrintApplyForm = {
    applyId: String(route.query.applyId)
  };
  if (!route.query.answers) {
    updatePrintStatus(formCorrect);
  }
};
const indexToLetter = (index) => {
  return String.fromCharCode(65 + Number(index)) + '、'; // 65对应ASCII码'A'
};
const indexToLetterAn = (indexStr) => {
  return [...indexStr].map((ch) => String.fromCharCode(65 + parseInt(ch, 10)) + ' ').join('');
};

// 判断所有选项是否都短
const isShortOptions = (optionVoList) => {
  if (!optionVoList) return true;
  // 去除HTML标签后判断长度
  return optionVoList.every((opt) => (opt.paperOption || '').replace(/<[^>]+>/g, '').length < 20);
};

// 分组函数
const groupOptions = (optionVoList, groupSize) => {
  if (!optionVoList) return [];
  const groups = [];
  for (let i = 0; i < optionVoList.length; i += groupSize) {
    groups.push(optionVoList.slice(i, i + groupSize));
  }
  return groups;
};

onMounted(async () => {
  await getPaperList(); // 确保数据加载完成
  //await qrcodeGen();
});
</script>
<style lang="scss">
.print-body {
  padding: 20px;
  background: #f6f6f6;
  // page-break-inside: avoid;
  // page-break-after: always;
  // break-after: page;
  break-after: always;
}
.print-container {
  width: 794px;
  margin: 0 auto;
  background: #fff;
  padding: 30px 50px;
  box-sizing: content-box;
  .code-img {
    width: 100px;
    height: 100px;
  }
}
.print-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #ddd;
  font-size: 12px;
  .header-logo {
    height: 40px;
    width: auto;
  }
}
.print-title {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  margin-top: 20px;
}
.print-button {
  text-align: center;
  margin-top: 20px;
}
.question-list {
  display: flex;
  flex-direction: column;
  gap: 40px;
  list-style: none;
  line-height: 1.75;
  font-size: 16px;
  font-family: '宋体', 'Times New Roman';
}
.question {
  display: flex;
  gap: 10px;
}
.option-list {
  display: flex;
  flex-direction: column;
}
.option-item {
  display: flex;
  gap: 10px;
  align-items: center;
}
.question-no {
  font-weight: bold;
}
.answer-section {
  margin-top: 20px;
}
.answer {
  display: flex;
  margin-top: 10px;
  font-size: 14px;
  font-family: '微软雅黑';
  .MathJye {
    white-space: nowrap;
    table {
      display: inline-block;
      vertical-align: middle;
    }
  }
}
.answer-label {
  font-weight: bold;
  white-space: nowrap;
  flex-shrink: 0;
}
@media print {
  @page {
    size: auto;
    margin: 0.5cm 1cm 2cm 1cm;

    @bottom-center {
      content: '- ' counter(page) ' -';
      font-size: 12px;
    }
    /* 页眉样式 */
    // @top-center {
    //   content: '';
    //   height: 50px;
    // }
  }

  .no-print {
    display: none !important;
  }

  /* 页眉样式 */
  .print-header {
    position: fixed;
    top: 0;
    width: 100%;
  }

  /* 确保主要内容不会被页眉页脚遮挡 */
  .print-body {
    margin-top: 60px;
    padding: 0;
    background: none;
  }
  .print-container {
    padding: 0;
  }
}
</style>
