<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="会员名" prop="nickName">
              <el-input v-model="queryParams.nickName" placeholder="请输入会员名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['printApply:printApply:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['printApply:printApply:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['printApply:printApply:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['printApply:printApply:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="printApplyList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="视频资源" align="center" prop="videoResourceName" />
        <el-table-column label="会员账号" align="center" prop="userName" />
        <el-table-column label="会员名" align="center" prop="nickName" />
        <el-table-column label="是否批改" align="center">
          <template #default="{ row }">
            <span>{{ row.correctStatus === 'Y' ? '已批改' : '未批改' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="是否打印" align="center" prop="status">
            <template #default="scope">
              <dict-tag :options="sys_yes_no" :value="scope.row.printStatus" />
            </template>
        </el-table-column>
        <el-table-column label="申请时间" align="center" prop="createTime" width="200px"/>
        
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip v-if="scope.row.pdfUrl" content="打印文件" placement="top">
              <el-button link type="primary" icon="printer" @click="handleUpdatePrintPdf(scope.row)" v-hasPermi="['printApply:printApply:edit']"></el-button>
            </el-tooltip>
            <el-tooltip v-if="!scope.row.pdfUrl" content="打印试题" placement="top">
              <el-button link type="primary" icon="printer" @click="handleUpdatePrint(scope.row)" v-hasPermi="['printApply:printApply:edit']"></el-button>
            </el-tooltip>
            <el-tooltip v-if="!scope.row.pdfUrl" content="打印答案解析" placement="top">
              <el-button link type="primary" icon="printer" @click="handleUpdatePrintAn(scope.row)" v-hasPermi="['printApply:printApply:edit']"></el-button>
            </el-tooltip>
            <el-tooltip v-if="!scope.row.pdfUrl" content="批改作业/查看批改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handlCorrectPrint(scope.row)" v-hasPermi="['printApply:printApply:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['printApply:printApply:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改打印申请对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="printApplyFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="课程ID" prop="courseId">
          <el-input v-model="form.courseId" placeholder="请输入课程ID" />
        </el-form-item>
        <el-form-item label="视频资源ID" prop="resourceId">
          <el-input v-model="form.resourceId" placeholder="请输入视频资源ID" />
        </el-form-item>
        <el-form-item label="知识点" prop="pointIds">
          <el-input v-model="form.pointIds" placeholder="请输入知识点" />
        </el-form-item>
        <el-form-item label="会员id" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入会员id" />
        </el-form-item>
        <el-form-item label="会员名" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入会员名" />
        </el-form-item>
        <el-form-item label="试题编号" prop="examNo">
          <el-input v-model="form.examNo" placeholder="请输入试题编号" />
        </el-form-item>
        <el-form-item label="批改后的url图片地址" prop="correctImageUrl">
          <el-input v-model="form.correctImageUrl" placeholder="请输入批改后的url图片地址" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

        <!-- 添加或修改打印申请对话框 -->
    <el-dialog :title="dialogPrint.title" v-model="dialogPrint.visible" width="500px" append-to-body>
      <el-row justify="center">
          <img :src="qrcodeDataURL" alt="二维码" />
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitFormPrint">确定打印</el-button>
          <el-button @click="cancelPrint">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="PrintApply" lang="ts">
import { listPrintApply, getPrintApply, delPrintApply, addPrintApply, updatePrintApply } from '@/api/printApply';
import { PrintApplyVO, PrintApplyQuery, PrintApplyForm } from '@/api/printApply/types';
import QRCode from 'qrcode'

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const qrcodeDataURL = ref('');

const { sys_yes_no } = toRefs<any>(proxy?.useDict('sys_yes_no'));

const printApplyList = ref<PrintApplyVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const printApplyFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const dialogPrint = reactive<DialogOption>({
  visible: false,
  title: ''
});
const initFormData: PrintApplyForm = {
  applyId: undefined,
  courseId: undefined,
  resourceId: undefined,
  pointIds: undefined,
  userId: undefined,
  userName: undefined,
  printStatus: undefined,
  examNo: undefined,
  correctImageUrl: undefined,
}
const data = reactive<PageData<PrintApplyForm, PrintApplyQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: undefined,
    params: {
    }
  },
  rules: {
    courseId: [
      { required: true, message: "课程ID不能为空", trigger: "blur" }
    ],
    resourceId: [
      { required: true, message: "视频资源ID不能为空", trigger: "blur" }
    ],
    pointIds: [
      { required: true, message: "知识点不能为空", trigger: "blur" }
    ],
    userId: [
      { required: true, message: "会员id不能为空", trigger: "blur" }
    ],
    userName: [
      { required: true, message: "会员名不能为空", trigger: "blur" }
    ],
    printStatus: [
      { required: true, message: "删除标志不能为空", trigger: "change" }
    ],
    examNo: [
      { required: true, message: "试题编号不能为空", trigger: "blur" }
    ],
    correctImageUrl: [
      { required: true, message: "批改后的url图片地址不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询打印申请列表 */
const getList = async () => {
  loading.value = true;
  const res = await listPrintApply(queryParams.value);
  printApplyList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

const cancelPrint = () => {
  dialogPrint.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  printApplyFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: PrintApplyVO[]) => {
  ids.value = selection.map(item => item.applyId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加打印申请";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: PrintApplyVO) => {
  reset();
  const _applyId = row?.applyId || ids.value[0]
  const res = await getPrintApply(_applyId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改打印申请";
}

const handleUpdatePrint = async (row?: PrintApplyVO) => {
  const fullPath = proxy.$router.resolve({
    path: '/print-apply/print-paper',
    query: { action: 'add', applyId: row.applyId }
  }).href;

  window.open(fullPath, '_blank');
    //const backendUrl = `${import.meta.env.VITE_APP_BACKEND_URL}/student/apply/myPage`;
    //window.open(backendUrl, '_blank');
}
const handleUpdatePrintPdf = async (row?: PrintApplyVO) => {
  window.open(row.pdfUrl, '_blank');
}
const handleUpdatePrintAn = async (row?: PrintApplyVO) => {
  const fullPath = proxy.$router.resolve({
    path: '/print-apply/print-paper',
    query: { action: 'add', applyId: row.applyId, answers:1}
  }).href;

  window.open(fullPath, '_blank');
    //const backendUrl = `${import.meta.env.VITE_APP_BACKEND_URL}/student/apply/myPage`;
    //window.open(backendUrl, '_blank');
}
const handlCorrectPrint = async (row?: PrintApplyVO) => {
  const fullPath = proxy.$router.resolve({
    path: '/print-apply/correct-paper',
    query: { action: 'add', applyId: row.applyId }
  }).href;

  window.open(fullPath, '_blank');

  // const text = `${import.meta.env.VITE_APP_BACKEND_URL}/student/apply/correctPage?applyId=${row.applyId}&userId=${row.userId}`
  // const width = 200;
  // const height = 200;
  // const correctLevel = 'M'; // 可选纠错等级：L/M/Q/H
  // try {
  //   const url = await QRCode.toDataURL(text, {
  //     width,
  //     height,
  //     errorCorrectionLevel: correctLevel
  //   });

  //   qrcodeDataURL.value = url;
  //   console.log('生成成功:', url);
  // } catch (error) {
  //   console.error('生成二维码失败:', error);
  // }
  //   dialogPrint.visible = true;
  //   dialogPrint.title = "批改作业";
}
/** 提交按钮 */
const submitForm = () => {
  printApplyFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.applyId) {
        await updatePrintApply(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addPrintApply(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}
const submitFormPrint = () => {
  dialogPrint.visible = true;
  dialogPrint.title = "打印";
}
/** 删除按钮操作 */
const handleDelete = async (row?: PrintApplyVO) => {
  const _applyIds = row?.applyId || ids.value;
  await proxy?.$modal.confirm('是否确认删除打印申请编号为"' + _applyIds + '"的数据项？').finally(() => loading.value = false);
  await delPrintApply(_applyIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('printApply/printApply/export', {
    ...queryParams.value
  }, `printApply_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
