<template>
  <div class="p-2">
    <el-row justify="center" style="margin-bottom: 10px;">
      <el-col :span="10" :offset="3">
        <span v-if="applyInfo" :style="{  fontSize: '1em' }">{{applyInfo.nickName}}({{applyInfo.userName}})</span>
      </el-col>
    </el-row>
    <el-row style="margin-top:20px;">
    </el-row>
    <el-row justify="center" v-for="(question, index) in paperList" :key="question.paperId" style="margin-bottom: 20px;">
      <!-- 题目 -->
      <el-col :span="21" :offset="0">
        <div style="overflow: hidden;margin-bottom: 20px;">
          <div style="font-size: 1em;float: left;">{{ index + 1 }}、 </div>
          <div style="font-size: 1em;float: left;" v-html="renderFormula(question.content)"></div>
        </div>
      </el-col>
      <!-- 对错选择 -->
      <el-col :span="21" :offset="3" style="margin-top: 10px;">
        <el-radio-group v-model="question.correct" size="large">
          <el-radio :value="1"><span :style="{ fontWeight: 'bold', fontSize: '0.7em' }">正确</span></el-radio>
          <el-radio :value="2"><span :style="{ fontWeight: 'bold', fontSize: '0.7em' }">错误</span></el-radio>
        </el-radio-group>
      </el-col>
    </el-row>

    <el-row justify="end">
      <el-col :span="15" :offset="3">
        <div class="dialog-footer">
          <el-button type="primary" @click="submitCorrectForm">提交</el-button>
      </div>  
      </el-col>
    </el-row>
     
  </div>
</template>

<script setup name="PaperPrint" lang="ts">
import { queryByApplyId } from '@/api/jyeoo/jyeooPaper';
import { JyeooPaperVO} from '@/api/jyeoo/jyeooPaper/types';
import { getPrintApply} from '@/api/printApply';
import { PrintApplyVO} from '@/api/printApply/types';
import { correctSave } from '@/api/jyeoo/mistakeNote';
import { CorrectSaveCmd } from '@/api/jyeoo/mistakeNote/types';
import katex from 'katex'
import 'katex/dist/katex.min.css'

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const route = useRoute();

const paperList = ref<JyeooPaperVO[]>([]);

const applyInfo = ref<PrintApplyVO>(null);

/** 查询试题 */
const getPaperList = async () => {
  const res = await queryByApplyId(String(route.query.applyId));
  paperList.value = res.data;

  const applyInfoRes = await getPrintApply(String(route.query.applyId));
  applyInfo.value = applyInfoRes.data;
}
const submitCorrectForm = async () => {
  // 假设 paperList 是 ref<JyeooPaperVO[]>(...)
  const errorPaperIds = paperList.value
  .filter(item => item.correct == 2) // 或者 item.correct === 2，根据你的值类型决定
  .map(item => String(item.paperId));
  if(!errorPaperIds){
    return;
  }
  let formCorrect:CorrectSaveCmd = {
    applyId: String(route.query.applyId),
    paperIds: errorPaperIds
  }
  await correctSave(formCorrect);
  proxy?.$modal.msgSuccess("操作成功"); 

}
const renderFormula = (str) => {
  return str.replace(/\$(.*?)\$/g, (_, formula) => {
    return katex.renderToString(formula, { throwOnError: false })
  })
}
onMounted(() => {
  getPaperList();
});
</script>
<style scoped>
.math-content {
  font-size: 1.2em;
  margin: 20px 0;
}
</style>
