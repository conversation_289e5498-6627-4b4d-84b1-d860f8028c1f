<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="学科编码" prop="subjectCode">
              <el-input v-model="queryParams.subjectCode" placeholder="请输入学科编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="学科名" prop="subjectName">
              <el-input v-model="queryParams.subjectName" placeholder="请输入学科名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['courseSubject:courseSubject:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['courseSubject:courseSubject:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['courseSubject:courseSubject:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['courseSubject:courseSubject:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="courseSubjectList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键ID" align="center" prop="subjectId" v-if="true" />
        <el-table-column label="学科编码" align="center" prop="subjectCode" />
        <el-table-column label="学科名" align="center" prop="subjectName" />
        <el-table-column label="最大试题数量" align="center" prop="questionMax" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['courseSubject:courseSubject:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['courseSubject:courseSubject:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改学科对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="courseSubjectFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="学科编码" prop="subjectCode">
          <el-input v-model="form.subjectCode" placeholder="请输入学科编码" />
        </el-form-item>
        <el-form-item label="学科名" prop="subjectName">
          <el-input v-model="form.subjectName" placeholder="请输入学科名" />
        </el-form-item>
        <el-form-item label="最大试题数量" prop="questionMax">
          <el-input v-model="form.questionMax" placeholder="请输入最大试题数量" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CourseSubject" lang="ts">
import { listCourseSubject, getCourseSubject, delCourseSubject, addCourseSubject, updateCourseSubject } from '@/api/courseSubject';
import { CourseSubjectVO, CourseSubjectQuery, CourseSubjectForm } from '@/api/courseSubject/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const courseSubjectList = ref<CourseSubjectVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const courseSubjectFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: CourseSubjectForm = {
  subjectId: undefined,
  subjectCode: undefined,
  subjectName: undefined,
  questionMax: undefined,
}
const data = reactive<PageData<CourseSubjectForm, CourseSubjectQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    subjectCode: undefined,
    subjectName: undefined,
    params: {
    }
  },
  rules: {
    subjectId: [
      { required: true, message: "主键ID不能为空", trigger: "blur" }
    ],
    subjectCode: [
      { required: true, message: "学科编码不能为空", trigger: "blur" }
    ],
    subjectName: [
      { required: true, message: "学科名不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询学科列表 */
const getList = async () => {
  loading.value = true;
  const res = await listCourseSubject(queryParams.value);
  courseSubjectList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  courseSubjectFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: CourseSubjectVO[]) => {
  ids.value = selection.map(item => item.subjectId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加学科";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: CourseSubjectVO) => {
  reset();
  const _subjectId = row?.subjectId || ids.value[0]
  const res = await getCourseSubject(_subjectId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改学科";
}

/** 提交按钮 */
const submitForm = () => {
  courseSubjectFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.subjectId) {
        await updateCourseSubject(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addCourseSubject(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: CourseSubjectVO) => {
  const _subjectIds = row?.subjectId || ids.value;
  await proxy?.$modal.confirm('是否确认删除学科编号为"' + _subjectIds + '"的数据项？').finally(() => loading.value = false);
  await delCourseSubject(_subjectIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('courseSubject/courseSubject/export', {
    ...queryParams.value
  }, `courseSubject_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
