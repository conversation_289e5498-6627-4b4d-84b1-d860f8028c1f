<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['aboutUs:aboutUs:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['aboutUs:aboutUs:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['aboutUs:aboutUs:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['aboutUs:aboutUs:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="aboutUsList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="描述" align="center" prop="usContent" :show-overflow-tooltip="true"/>
        <el-table-column label="状态" align="center" prop="usStatus">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.usStatus"/>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['aboutUs:aboutUs:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['aboutUs:aboutUs:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改关于我们对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="1000px" append-to-body>
      <el-form ref="aboutUsFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="状态" prop="usStatus">
          <el-select v-model="form.usStatus" placeholder="请选择1启用，0禁用">
            <el-option
                v-for="dict in sys_normal_disable"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <editor v-model="form.usContent" :min-height="192"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="AboutUs" lang="ts">
import { listAboutUs, getAboutUs, delAboutUs, addAboutUs, updateAboutUs } from '@/api/aboutUs';
import { AboutUsVO, AboutUsQuery, AboutUsForm } from '@/api/aboutUs/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_normal_disable } = toRefs<any>(proxy?.useDict('sys_normal_disable'));

const aboutUsList = ref<AboutUsVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const aboutUsFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: AboutUsForm = {
  usId: undefined,
  usContent: undefined,
  usStatus: undefined,
}
const data = reactive<PageData<AboutUsForm, AboutUsQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    params: {
    }
  },
  rules: {
    usContent: [
      { required: true, message: "描述不能为空", trigger: "blur" }
    ],
    usStatus: [
      { required: true, message: "1启用，0禁用不能为空", trigger: "change" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询关于我们列表 */
const getList = async () => {
  loading.value = true;
  const res = await listAboutUs(queryParams.value);
  aboutUsList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  aboutUsFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: AboutUsVO[]) => {
  ids.value = selection.map(item => item.usId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加关于我们";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: AboutUsVO) => {
  reset();
  const _usId = row?.usId || ids.value[0]
  const res = await getAboutUs(_usId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改关于我们";
}

/** 提交按钮 */
const submitForm = () => {
  aboutUsFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.usId) {
        await updateAboutUs(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addAboutUs(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: AboutUsVO) => {
  const _usIds = row?.usId || ids.value;
  await proxy?.$modal.confirm('是否确认删除关于我们编号为"' + _usIds + '"的数据项？').finally(() => loading.value = false);
  await delAboutUs(_usIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('aboutUs/aboutUs/export', {
    ...queryParams.value
  }, `aboutUs_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
