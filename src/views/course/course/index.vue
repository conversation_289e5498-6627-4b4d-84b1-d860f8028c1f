<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="课程名称" prop="courseName">
              <el-input v-model="queryParams.courseName" placeholder="请输入课程名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="副标题" prop="subTitle">
              <el-input v-model="queryParams.subTitle" placeholder="请输入副标题" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="班级类型" prop="typeId">
              <el-select v-model="queryParams.typeId" placeholder="请选择班级类型" clearable>
                <el-option v-for="classtype in classTypeList" :key="classtype.typeId" :label="classtype.typeName" :value="classtype.typeId" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:course:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['system:course:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['system:course:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:course:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="courseList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="课程名称" align="center" prop="courseName" />
        <el-table-column label="副标题" align="center" prop="subTitle" />
        <el-table-column label="介绍" align="center" prop="description" width="150"  :show-overflow-tooltip="true"/>
        <el-table-column label="班级类型" align="center" prop="typeId" :formatter="typeClassFormatter">
        </el-table-column>
        <el-table-column label="推荐图" align="center" prop="recommendImage" />
        <el-table-column label="列表图" align="center" prop="listImage" />
        <el-table-column label="详情图" align="center" prop="detailImage" />
        <el-table-column label="是否置顶" align="center" prop="recommendStatus">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.recommendStatus"/>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:course:edit']"></el-button>
            </el-tooltip>
             <el-tooltip content="版本维护" placement="top">
              <el-button link type="primary" icon="Setting" @click="handleVersion(scope.row)" v-hasPermi="['course:courseSubjectVersion:edit']"></el-button>
            </el-tooltip>
             <el-tooltip content="名师维护" placement="top">
              <el-button link type="primary" icon="User" @click="handleTeacher(scope.row)" v-hasPermi="['system:course:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="单元维护" placement="top">
              <el-button link type="primary" icon="Folder" @click="handleUnit(scope.row)" v-hasPermi="['system:course:unit']"></el-button>
            </el-tooltip>
            <el-tooltip content="视频资源" placement="top">
              <el-button link type="primary" icon="Film" @click="handleResource(scope.row)" v-hasPermi="['system:course:video']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:course:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改课程管理对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body>
      <el-form ref="courseFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="课程名称" prop="courseName">
          <el-input v-model="form.courseName" placeholder="请输入课程名称" />
        </el-form-item>
        <el-form-item label="副标题" prop="subTitle">
          <el-input v-model="form.subTitle" placeholder="请输入副标题" />
        </el-form-item>
        <el-form-item label="介绍" prop="description">
            <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="班级类型" prop="typeId">
          <el-select v-model="form.typeId" placeholder="请选择班级类型" clearable>
            <el-option v-for="classtype in classTypeList" :key="classtype.typeId" :label="classtype.typeName"
                      :value="classtype.typeId"/>
          </el-select>
        </el-form-item>
        <el-form-item label="是否置顶" prop="recommendStatus">
          <el-select v-model="form.recommendStatus" placeholder="请选择是否置">
            <el-option
                v-for="dict in sys_yes_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="推荐图" prop="recommendImage">
          <image-upload v-model="form.recommendImage" :limit="1"/>
        </el-form-item>
        <el-form-item label="列表图" prop="listImage">
          <image-upload v-model="form.listImage" :limit="1"/>
        </el-form-item>
        <el-form-item label="详情图" prop="detailImage">
          <image-upload v-model="form.detailImage" :limit="1"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-if="courseSubjectDialog.visible" v-model="courseSubjectDialog.visible" :title="courseSubjectDialog.title" destroy-on-close append-to-body width="1000px">
      <CourseSubjectVersion :param="courseIdParam" />
    </el-dialog>
    <el-dialog  v-if="courseTeacherDialog.visible" v-model="courseTeacherDialog.visible" :title="courseTeacherDialog.title" destroy-on-close append-to-body width="1000px">
      <CourseSubjectTeacher :param="courseIdTeacherParam" />
    </el-dialog>

    <el-dialog  v-if="subjectUnitDialog.visible" v-model="subjectUnitDialog.visible" :title="subjectUnitDialog.title" destroy-on-close append-to-body width="1000px">
      <CourseSubjectUnit :param="courseIdUnitParam" />
    </el-dialog>
  </div>
</template>

<script setup name="Course" lang="ts">
import { listCourse, getCourse, delCourse, addCourse, updateCourse } from '@/api/course/course';
import { CourseVO, CourseQuery, CourseForm } from '@/api/course/course/types';
import {optionSelectClassType} from '@/api/course/classType';
import { CourseSubjectVO,CourseSubjectQuery } from '@/api/courseSubject/types';
import { queryListSubject} from '@/api/courseSubject';
import CourseSubjectVersion from '@/views/course/courseSubjectVersion/index.vue';
import CourseSubjectTeacher from '@/views/course/courseSubjectTeacher/index.vue';
import CourseSubjectUnit from '@/views/course/courseSubjectUnit/index.vue';


const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_yes_no } = toRefs<any>(proxy?.useDict('sys_yes_no'));

const courseList = ref<CourseVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const courseFormRef = ref<ElFormInstance>();

const classTypeList = ref([]); //班型选择

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: CourseForm = {
  courseId: undefined,
  courseName: undefined,
  subTitle: undefined,
  description: undefined,
  typeId: undefined,
  recommendImage: undefined,
  listImage: undefined,
  detailImage: undefined,
  recommendStatus: undefined,
  subjects: undefined,
}
const data = reactive<PageData<CourseForm, CourseQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    courseName: undefined,
    subTitle: undefined,
    params: {
    }
  },
  rules: {
    courseName: [
      { required: true, message: "课程名称不能为空", trigger: "blur" }
    ],
    subTitle: [
      { required: true, message: "副标题不能为空", trigger: "blur" }
    ],
    description: [
      { required: true, message: "介绍不能为空", trigger: "blur" }
    ],
    typeId: [
      { required: true, message: "班级类型不能为空", trigger: "blur" }
    ],
    listImage: [
      { required: true, message: "列表图不能为空", trigger: "blur" }
    ],
    detailImage: [
      { required: true, message: "详情图L不能为空", trigger: "blur" }
    ],
    recommendStatus: [
      { required: true, message: "是否置顶", trigger: "change" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询课程管理列表 */
const getList = async () => {
  loading.value = true;
  const res = await listCourse(queryParams.value);
  courseList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  courseFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: CourseVO[]) => {
  ids.value = selection.map(item => item.courseId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加课程管理";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: CourseVO) => {
  reset();
  const _courseId = row?.courseId || ids.value[0]
  const res = await getCourse(_courseId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改课程管理";
}

/** 修改按钮操作 */
const handleResource = async (row?: CourseVO) => {
  proxy.$router.push({
    path: '/course/course-resource/index/'+row?.courseId, // 对应路由配置的path
    query: { action: 'add' } // 标识新增操作
  });
}


/** 提交按钮 */
const submitForm = () => {
  console.log(form.value);
  courseFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.courseId) {
        await updateCourse(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addCourse(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: CourseVO) => {
  const _courseIds = row?.courseId || ids.value;
  await proxy?.$modal.confirm('是否确认删除课程管理编号为"' + _courseIds + '"的数据项？').finally(() => loading.value = false);
  await delCourse(_courseIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('system/course/export', {
    ...queryParams.value
  }, `course_${new Date().getTime()}.xlsx`)
}
const initOptionClassType = async () => {
    const classTypeRes = await optionSelectClassType();
    classTypeList.value = classTypeRes.data;
};

const idToClassTypeNameMap = computed(() => {
  return classTypeList.value.reduce((map, item) => {  
    map[item.typeId] = item.typeName;  
    return map;  
  }, {});  
});
const typeClassFormatter = (row, column, cellValue) => {  
  if(!cellValue){
    return;
  }
  return idToClassTypeNameMap.value[cellValue] || '';  
};

const subjectOptions = ref<CourseSubjectVO[]>([]);

const initSubjectOptions= async () => {

  const query: CourseSubjectQuery = {
      pageNum: 1,
      pageSize: 10,
      subjectCode: undefined,
      subjectName: undefined,
      params: undefined
    };

    const rest = await queryListSubject(query);
    subjectOptions.value = rest.data;
}

const courseIdParam = ref<string|number>('');
const courseSubjectDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const handleVersion = async (row?: CourseVO) => {
  reset();
  const _courseId = row?.courseId;
  courseIdParam.value = _courseId;
  courseSubjectDialog.visible = true;
  courseSubjectDialog.title = "版本维护";
}
const courseIdTeacherParam = ref<string|number>('');
const courseTeacherDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const handleTeacher = async (row?: CourseVO) => {
  reset();
  const _courseId = row?.courseId;
  courseIdTeacherParam.value = _courseId;
  courseTeacherDialog.visible = true;
  courseTeacherDialog.title = "名师维护";
};

const subjectUnitDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const courseIdUnitParam = ref<string|number>('');
const handleUnit = async (row?: CourseVO) => {
  reset();
  const _courseId = row?.courseId;
  courseIdUnitParam.value = _courseId;
  subjectUnitDialog.visible = true;
  subjectUnitDialog.title = "单元维护";
}

onMounted(() => {
  initOptionClassType();
  getList();
  initSubjectOptions();
});
</script>
