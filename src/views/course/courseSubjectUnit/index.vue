<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="单元名称" prop="unitName">
              <el-input v-model="queryParams.unitName" placeholder="请输入单元名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['course:courseSubjectUnit:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['course:courseSubjectUnit:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['course:courseSubjectUnit:remove']">删除</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="courseSubjectUnitList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="科目" align="center" prop="subjectName" />
        <el-table-column label="版本" align="center" prop="versionName" />
        <el-table-column label="单元名称" align="center" prop="unitName" />
        <el-table-column label="单元顺序" align="center" prop="orderNum" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['course:courseSubjectUnit:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['course:courseSubjectUnit:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改课程单元对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="courseSubjectUnitFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="选择科目" prop="subjectId">
          <el-select v-model="form.subjectId" placeholder="请选择" @change="subjectChange">
            <el-option
              v-for="item in subjectOptions"
              :key="item.subjectId"
              :label="item.subjectName"
              :value="item.subjectId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择版本" prop="versionId">
          <el-select v-model="form.versionId" placeholder="请选择">
            <el-option
              v-for="item in versionOptions"
              :key="item.versionId"
              :label="item.versionName"
              :value="item.versionId"
            ></el-option>
          </el-select>
        </el-form-item>
         <el-form-item label="顺序" prop="orderNum">
          <el-input v-model="form.orderNum" placeholder="请输入单元顺序" />
        </el-form-item>
        <el-form-item label="单元名称" prop="unitName">
          <el-input v-model="form.unitName" placeholder="请输入单元名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CourseSubjectUnit" lang="ts">
import { listCourseSubjectUnit, getCourseSubjectUnit, delCourseSubjectUnit, addCourseSubjectUnit, updateCourseSubjectUnit } from '@/api/course/courseSubjectUnit';
import { CourseSubjectUnitVO, CourseSubjectUnitQuery, CourseSubjectUnitForm } from '@/api/course/courseSubjectUnit/types';
import { listSubject,listVersion } from '@/api//course/courseSubjectVersion';
import { CourseSubjectVO,CourseSubjectQuery } from '@/api/courseSubject/types';
import { listCourseSubjectVersion} from '@/api//course/courseSubjectVersion';
import { CourseSubjectVersionVO } from '@/api/course/courseSubjectVersion/types';
import { propTypes } from '@/utils/propTypes';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const props = defineProps({
  param: propTypes.any.isRequired
});


const courseSubjectUnitList = ref<CourseSubjectUnitVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const courseSubjectUnitFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: CourseSubjectUnitForm = {
  unitId: undefined,
  courseId: undefined,
  subjectId: undefined,
  versionId: undefined,
  unitName: undefined,
}
const data = reactive<PageData<CourseSubjectUnitForm, CourseSubjectUnitQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    courseId: undefined,
    subjectId: undefined,
    versionId: undefined,
    unitName: undefined,
    params: {
    }
  },
  rules: {
    courseId: [
      { required: true, message: "课程ID不能为空", trigger: "blur" }
    ],
    subjectId: [
      { required: true, message: "科目ID不能为空", trigger: "blur" }
    ],
    versionId: [
      { required: true, message: "版本ID不能为空", trigger: "blur" }
    ],
    unitName: [
      { required: true, message: "单元名称不能为空", trigger: "blur" }
    ],
    orderNum: [
      { required: true, message: "顺序不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询课程单元列表 */
const getList = async () => {
  loading.value = true;
  const courseId = computed(() => props.param);
  queryParams.value.courseId = courseId.value;
  const res = await listCourseSubjectUnit(queryParams.value);
  courseSubjectUnitList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  courseSubjectUnitFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: CourseSubjectUnitVO[]) => {
  ids.value = selection.map(item => item.unitId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加课程单元";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: CourseSubjectUnitVO) => {
  reset();
  const _unitId = row?.unitId || ids.value[0]
  const res = await getCourseSubjectUnit(_unitId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改课程单元";
}

/** 提交按钮 */
const submitForm = () => {
  courseSubjectUnitFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      const courseId = computed(() => props.param);
      form.value.courseId = courseId.value;
      if (form.value.unitId) {
        await updateCourseSubjectUnit(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addCourseSubjectUnit(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: CourseSubjectUnitVO) => {
  const _unitIds = row?.unitId || ids.value;
  await proxy?.$modal.confirm('是否确认删除课程单元编号为"' + _unitIds + '"的数据项？').finally(() => loading.value = false);
  await delCourseSubjectUnit(_unitIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('course/courseSubjectUnit/export', {
    ...queryParams.value
  }, `courseSubjectUnit_${new Date().getTime()}.xlsx`)
}

//-------------------科目选择-------------------------------------
const subjectOptions = ref<CourseSubjectVO[]>([]);
const initSubjectOptions= async () => {
    const courseId = computed(() => props.param);
    const query: CourseSubjectQuery = {
      pageNum: 1,
      pageSize: 10,
      subjectCode: undefined,
      subjectName: undefined,
      courseId:courseId.value,
      params: undefined
    };
    const rest = await listSubject(query);
    subjectOptions.value = rest.data;
}
const subjectChange = async (value: number | string) => {
    versionOptions.value = [];
    //如果有变化,清空下拉列表中key、value
    form.value.versionId = '';
    if(value){
      initVersionOptions();
    }
};
//-------------------科目选择-------------------------------------


//------------------版本选择--------------------------------------
const versionOptions = ref<CourseSubjectVersionVO[]>([]);
const initVersionOptions= async () => {
    const courseId = computed(() => props.param);
    const subjectId = form.value.subjectId;
    if(!subjectId){
      return;
    }
    const query: CourseSubjectQuery = {
      pageNum: 1,
      pageSize: 10,
      subjectId: subjectId,
      courseId:courseId.value,
      params: undefined
    };
    const rest = await listVersion(query);
    versionOptions.value = rest.data;
}
//------------------版本选择--------------------------------------


onMounted(() => {
  getList();
  initSubjectOptions();
});
</script>
