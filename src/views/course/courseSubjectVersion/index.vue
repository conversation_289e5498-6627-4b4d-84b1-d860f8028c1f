<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="选择科目" prop="subjectId">
              <el-select v-model="queryParams.subjectId" placeholder="请选择">
                <el-option
                  v-for="item in subjectOptions"
                  :key="item.subjectId"
                  :label="item.subjectName"
                  :value="item.subjectId"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="版本名称" prop="versionName">
              <el-input v-model="queryParams.versionName" placeholder="请输入版本名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['course:courseSubjectVersion:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['course:courseSubjectVersion:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['course:courseSubjectVersion:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['course:courseSubjectVersion:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="courseSubjectVersionList" @selection-change="handleSelectionChange">
        <el-table-column label="科目" align="center" prop="subjectName" />
        <el-table-column label="版本名称" align="center" prop="versionName" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['course:courseSubjectVersion:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['course:courseSubjectVersion:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改课程版本对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="courseSubjectVersionFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="选择科目" prop="subjectId">
          <el-select v-model="form.subjectId" placeholder="请选择">
            <el-option
              v-for="item in subjectOptions"
              :key="item.subjectId"
              :label="item.subjectName"
              :value="item.subjectId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="版本名称" prop="versionName">
          <el-input v-model="form.versionName" placeholder="请输入版本名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CourseSubjectVersion" lang="ts">
import { listCourseSubjectVersion, getCourseSubjectVersion, delCourseSubjectVersion, addCourseSubjectVersion, updateCourseSubjectVersion } from '@/api//course/courseSubjectVersion';
import { CourseSubjectVersionVO, CourseSubjectVersionQuery, CourseSubjectVersionForm } from '@/api/course/courseSubjectVersion/types';
import { propTypes } from '@/utils/propTypes';
import { CourseSubjectVO,CourseSubjectQuery } from '@/api/courseSubject/types';
import { queryListSubject} from '@/api/courseSubject';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const courseSubjectVersionList = ref<CourseSubjectVersionVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const courseSubjectVersionFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: CourseSubjectVersionForm = {
  versionId: undefined,
  courseId: undefined,
  subjectId: undefined,
  versionName: undefined,
}
const data = reactive<PageData<CourseSubjectVersionForm, CourseSubjectVersionQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    courseId: undefined,
    subjectId: undefined,
    versionName: undefined,
    params: {
    }
  },
  rules: {
    subjectId: [
      { required: true, message: "科目ID不能为空", trigger: "change" }
    ],
    versionName: [
      { required: true, message: "版本名称不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

const props = defineProps({
  param: propTypes.any.isRequired
});
/** 查询课程版本列表 */
const getList = async () => {
  loading.value = true;
  const courseId = computed(() => props.param);
  queryParams.value.courseId = courseId.value;
  const res = await listCourseSubjectVersion(queryParams.value);
  courseSubjectVersionList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  courseSubjectVersionFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: CourseSubjectVersionVO[]) => {
  ids.value = selection.map(item => item.versionId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加课程版本";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: CourseSubjectVersionVO) => {
  reset();
  const _versionId = row?.versionId || ids.value[0]
  const res = await getCourseSubjectVersion(_versionId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改课程版本";
}

/** 提交按钮 */
const submitForm = () => {
  courseSubjectVersionFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const courseId = computed(() => props.param);
      form.value.courseId = courseId.value;
      buttonLoading.value = true;
      if (form.value.versionId) {
        await updateCourseSubjectVersion(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addCourseSubjectVersion(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: CourseSubjectVersionVO) => {
  const _versionIds = row?.versionId || ids.value;
  await proxy?.$modal.confirm('是否确认删除课程版本编号为"' + _versionIds + '"的数据项？').finally(() => loading.value = false);
  await delCourseSubjectVersion(_versionIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('course/courseSubjectVersion/export', {
    ...queryParams.value
  }, `courseSubjectVersion_${new Date().getTime()}.xlsx`)
}

const subjectOptions = ref<CourseSubjectVO[]>([]);
const initSubjectOptions= async () => {
    const query: CourseSubjectQuery = {
      pageNum: 1,
      pageSize: 10,
      subjectCode: undefined,
      subjectName: undefined,
      params: undefined
    };
    const rest = await queryListSubject(query);
    subjectOptions.value = rest.data;
}
onMounted(() => {
  getList();
  initSubjectOptions();
});
</script>
