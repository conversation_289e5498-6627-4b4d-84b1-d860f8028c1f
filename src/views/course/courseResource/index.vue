<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-row  class="mb8">
            <el-col :span="0.5">
            </el-col>
            <el-col :span="3">
              <image-show :src="courseInfo.detailImage" :width="150" :height="150"/>
            </el-col>
            <el-col :span="10">
              <el-row  class="mb8">
                <el-col :span="24">
                  <el-form-item label="课程名称" class="no-vertical-margin" label-width="80px">
                    <span>{{courseInfo.courseName}}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="副标题" class="no-vertical-margin" label-width="80px">
                    <span>{{courseInfo.subTitle}}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="介绍" class="no-vertical-margin" label-width="80px">
                    <span>{{courseInfo.description}}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </el-card>
      </div>
    </transition>
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="130px">
            <el-row>
              <el-col :span="7">
                <el-form-item label="视频资源名称" prop="videoResourceName">
                  <el-input v-model="queryParams.videoResourceName" placeholder="请输入视频资源名称" clearable @keyup.enter="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="版本" prop="versionId">
                  <el-select v-model="queryParams.versionId" placeholder="请选择版本" clearable @change="versionQueryOptionsChange">
                    <el-option v-for="version in versionQueryOptions" :key="version.versionId" :label="version.versionName" :value="version.versionId" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="名师" prop="teacherId">
                  <el-select v-model="queryParams.teacherId" placeholder="请选择名师" clearable>
                    <el-option v-for="teacher in teacherQueryOptions" :key="teacher.teacherId" :label="teacher.teacherName" :value="teacher.teacherId" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="单元" prop="unitId">
                  <el-select v-model="queryParams.unitId" placeholder="请选择单元" clearable>
                    <el-option v-for="unit in unitQueryOptions" :key="unit.unitId" :label="unit.unitName" :value="unit.unitId" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
             <el-row>
              <el-col :span="6" :offset="1" class="search-btns">
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                  <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:courseResource:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['system:courseResource:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['system:courseResource:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:courseResource:export']">导出</el-button>
          </el-col>
          <el-col :span="1.5">
                <el-dropdown class="mt-[1px]">
                  <el-button plain type="info">
                    更多
                    <el-icon class="el-icon--right"><arrow-down /></el-icon
                  ></el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item icon="Download" @click="importTemplate">下载模板</el-dropdown-item>
                      <el-dropdown-item icon="Top" @click="handleImport"> 导入数据</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="courseResourceList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="视频资源名称" align="center" prop="videoResourceName" />
        <el-table-column label="科目" align="center" prop="subjectName">
        </el-table-column>
        <el-table-column label="版本" align="center" prop="versionName">
        </el-table-column>
        <el-table-column label="名师" align="center" prop="teacherName" />
        <el-table-column label="视频课链接" align="center" prop="videoCourseLink" :show-overflow-tooltip="true">
          <template #default="scope">
            <el-link type="primary" :href="scope.row.videoCourseLink" target="_blank" v-if="scope.row.videoCourseLink">
              {{ scope.row.videoCourseLink }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="讲义链接" align="center" prop="courseNotes" :show-overflow-tooltip="true">
          <template #default="scope">
            <el-link type="primary" :href="scope.row.courseNotes" target="_blank" v-if="scope.row.courseNotes">
              {{ scope.row.courseNotes }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="视频时长分钟" align="center" prop="videoMinute" />
        <el-table-column label="视频时长秒" align="center" prop="videoSecond" />
        <el-table-column label="显示顺序" align="center" prop="orderNum" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:courseResource:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="预览试题" placement="top">
              <el-button link type="primary" icon="printer" @click="handlePrint(scope.row)" v-hasPermi="['system:courseResource:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:courseResource:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改课程资源对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="700px" append-to-body>
      <el-form ref="courseResourceFormRef" :model="form" :rules="rules" label-width="130px">
        <el-form-item label="视频资源名称" prop="videoResourceName">
          <el-input v-model="form.videoResourceName" placeholder="请输入视频资源名称" />
        </el-form-item>
        <el-form-item label="选择科目" prop="subjectId">
          <el-select v-model="form.subjectId" placeholder="请选择" @change="subjectChange">
            <el-option
              v-for="item in subjectOptions"
              :key="item.subjectId"
              :label="item.subjectName"
              :value="item.subjectId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择版本" prop="versionId">
          <el-select v-model="form.versionId" placeholder="请选择"  @change="versionChange">
            <el-option
              v-for="item in versionOptions"
              :key="item.versionId"
              :label="item.versionName"
              :value="item.versionId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择名师" prop="teacherId">
          <el-select v-model="form.teacherId" placeholder="请选择">
            <el-option
              v-for="item in teacherOptions"
              :key="item.teacherId"
              :label="item.teacherName"
              :value="item.teacherId"
            ></el-option>
          </el-select>
        </el-form-item>
         <el-form-item label="选择单元" prop="unitId">
          <el-select v-model="form.unitId" placeholder="请选择">
            <el-option
              v-for="item in unitOptions"
              :key="item.unitId"
              :label="item.unitName"
              :value="item.unitId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="视频讲义" prop="courseNotesFile">
          <fileUpload v-model="form.courseNotesFile" :file-type="['pdf']" :file-size="'20'" :limit="1" />
        </el-form-item>
         <el-form-item label="视频课" prop="videoCourseFile">
          <video-upload v-model="form.videoCourseFile" :limit="1" :file-size="'1024'"/>
        </el-form-item>
        <el-form-item label="菁优试题" prop="jyeooPaperIds">
          <el-input type="textarea" v-model="form.jyeooPaperIds" placeholder="请输入显示菁优试题多个以英文逗号(,)隔开" :rows="15"/>
        </el-form-item>
        <el-form-item label="视频时长分钟" prop="videoMinute">
          <el-input v-model="form.videoMinute" placeholder="请输入视频时长分钟" />
        </el-form-item>
        <el-form-item label="视频时长秒" prop="videoSecond">
          <el-input v-model="form.videoSecond" placeholder="请输入视频时长秒" />
        </el-form-item>
        <el-form-item label="显示顺序" prop="orderNum">
          <el-input v-model="form.orderNum" placeholder="请输入显示顺序" />
        </el-form-item>
        <el-form-item label="考点" prop="pointIdList">
          <el-tree
            class="tree-border"
            ref="pointsRef"
            :props="{label: 'pointName', children: 'children' }"
            :data="treeNodes"
            node-key="pointId"
            v-model="form.pointIdList"
            :default-expanded-keys="form.pointIdList"
            :default-checked-keys="form.pointIdList"
            check-strictly
            show-checkbox>
          </el-tree>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>


    <!-- 课程资源导入 -->
    <el-dialog v-model="upload.open" :title="upload.title" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport + '&courseId='+courseId "
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload">
          <i-ep-upload-filled />
        </el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="text-center el-upload__tip">
            <div class="el-upload__tip" style="display:none;"><el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据</div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CourseResource" lang="ts">
import { listCourseResource, getCourseResource, delCourseResource, addCourseResource, updateCourseResource } from '@/api/course/courseResource';
import { CourseResourceVO, CourseResourceQuery, CourseResourceForm } from '@/api/course/courseResource/types';
import { getCourse} from '@/api/course/course';
import { CourseVO} from '@/api/course/course/types';
import { globalHeaders } from '@/utils/request';
import { queryPointList} from '@/api/point';
import { PointVO, PointQuery } from '@/api/point/types';
import { CourseSubjectVO ,CourseSubjectQuery} from '@/api/courseSubject/types';
import { listSubject,listVersion } from '@/api//course/courseSubjectVersion';
import { CourseSubjectVersionVO } from '@/api/course/courseSubjectVersion/types';
import { listTeacher } from '@/api/course/courseSubjectTeacher';
import { CourseSubjectTeacherQuery, CourseSubjectTeacherVO} from '@/api/course/courseSubjectTeacher/types';
import { CourseSubjectUnitQuery, CourseSubjectUnitVO } from '@/api/course/courseSubjectUnit/types';
import { listUnit } from '@/api/course/courseSubjectUnit';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_course_syllabus, sys_course_subject, sys_course_version } = toRefs<any>(proxy?.useDict('sys_course_syllabus', 'sys_course_subject', 'sys_course_version'));

const courseResourceList = ref<CourseResourceVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const courseResourceFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const pointsRef = ref<ElTreeInstance>();

const initFormData: CourseResourceForm = {
  resourceId: undefined,
  courseId: undefined,
  videoResourceName: undefined,
  subjectId: undefined,
  courseVersion: undefined,
  courseTeacher: undefined,
  courseSyllabus: undefined,
  videoCourseLink: undefined,
  courseNotes: undefined,
  courseExercises: undefined,
  videoMinute: undefined,
  videoSecond: undefined,
  orderNum: undefined,
  pointIds: undefined,
  pointExpandedKeys: undefined,
  pointIdList: undefined,
  jyeooPaperIds: undefined,
  courseNotesFile: undefined,
  videoCourseFile: undefined,
}
const data = reactive<PageData<CourseResourceForm, CourseResourceQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    videoResourceName: undefined,
    courseId: undefined,
    params: {
    }
  },
  rules: {
    courseId: [
      { required: true, message: "课程id不能为空", trigger: "blur" }
    ],
    videoResourceName: [
      { required: true, message: "视频资源名称不能为空", trigger: "blur" }
    ],
    subjectId: [
      { required: true, message: "科目不能为空", trigger: "change" }
    ],
    versionId: [
      { required: true, message: "版本不能为空", trigger: "change" }
    ],
    teacherId: [
      { required: true, message: "名师不能为空", trigger: "blur" }
    ],
    videoCourseFile: [
      { required: true, message: "视频不能为空", trigger: "blur" }
    ],
    videoMinute: [
      { required: true, message: "视频时长分钟不能为空", trigger: "blur" }
    ],
    videoSecond: [
      { required: true, message: "视频时长秒不能为空", trigger: "blur" }
    ],
    orderNum: [
      { required: true, message: "显示顺序不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

const route = useRoute();

const courseId = ref<string | number>('');

const treeNodes = ref<PointVO[]>([]);

/** 查询课程资源列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.courseId = courseId.value;
  const res = await listCourseResource(queryParams.value);
  courseResourceList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  courseResourceFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: CourseResourceVO[]) => {
  ids.value = selection.map(item => item.resourceId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  loadTreeNode();
  dialog.visible = true;
  dialog.title = "添加课程资源";
}
/** 修改按钮操作 */
const handleUpdate = async (row?: CourseResourceVO) => {
  //clearTree();
 // pointsRef.value?.setCheckedKeys([]);
  reset();
  const _resourceId = row?.resourceId || ids.value[0]
  const res = await getCourseResource(_resourceId);
  Object.assign(form.value, res.data);
  console.log(form.value);
  //form.value.pointExpandedKeys=["1921837691113816065","1921837777193517058"];
  //form.value.pointIdList = ["1921837777193517058"];
  dialog.visible = true;
  dialog.title = "修改课程资源";
  setTimeout(() => {
      initVersionOptions();
      initTeacherOptions();
      initUnitOptions();
      loadTreeNode();
  }, 200);
}

/** 预览试题 */
const handlePrint = async (row?: CourseResourceVO) => {
  const _resourceId = row?.resourceId || ids.value[0];
  const fullPath = proxy.$router.resolve({
    path: '/print-apply/course-resource-paper',
    query: { action: 'add', resourceId: _resourceId }
  }).href;

  window.open(fullPath, '_blank');
}

// 获取所有选中的节点 key（pointId）
const getLeafKeys = () => {
  const tree = pointsRef.value;
  if (!tree) return [];

  // 获取选中的节点
  const leafNodes = tree.getCheckedNodes(false, false);
  const leafKeys = leafNodes.map(node => node.pointId);
  return leafKeys;
};
/** 提交按钮 */
const submitForm = () => {
  courseResourceFormRef.value?.validate(async (valid: boolean) => {
      // 目前被选中的部门节点
    let checkedKeys = getLeafKeys();
    // 半选中的部门节点
    form.value.pointIdList = checkedKeys;
    if(checkedKeys && checkedKeys.length>0){
     form.value.pointIds = checkedKeys.join(',')
    }
    if (valid) {
        buttonLoading.value = true;
        form.value.courseId = courseId.value;
        if (form.value.resourceId) {
          await updateCourseResource(form.value).finally(() =>  buttonLoading.value = false);
        } else {
          await addCourseResource(form.value).finally(() =>  buttonLoading.value = false);
        }
        proxy?.$modal.msgSuccess("操作成功");
        dialog.visible = false;
        await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: CourseResourceVO) => {
  const _resourceIds = row?.resourceId || ids.value;
  await proxy?.$modal.confirm('是否确认删除课程资源编号为"' + _resourceIds + '"的数据项？').finally(() => loading.value = false);
  await delCourseResource(_resourceIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('system/courseResource/export', {
    ...queryParams.value
  }, `courseResource_${new Date().getTime()}.xlsx`)
}
/** 修改按钮操作 */
const courseInfo = ref<CourseVO>({});
const getCourseInfo = async () => {
  const res = await getCourse(courseId?.value);
  Object.assign(courseInfo.value, res.data);
}

/** 下载模板操作 */
const importTemplate = () => {
  proxy?.download('/system/courseResource/importTemplate', {}, `course_resource_${new Date().getTime()}.xlsx`);
};
const uploadRef = ref<ElUploadInstance>();
const upload = reactive<ImportOption>({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: '',
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: globalHeaders(),
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + '/system/courseResource/importData'
});
/**文件上传中处理 */
const handleFileUploadProgress = () => {
  upload.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response: any, file: UploadFile) => {
  upload.open = false;
  upload.isUploading = false;
  uploadRef.value?.handleRemove(file);
  ElMessageBox.alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + '</div>', '导入结果', {
    dangerouslyUseHTMLString: true
  });
  getList();
};
/** 导入按钮操作 */
const handleImport = () => {
  upload.title = '用户导入';
  upload.open = true;
};
/** 提交上传文件 */
function submitFileForm() {
  uploadRef.value?.submit();
}

const queryParamsPoint = ref<PointQuery>({
    pageNum: 1,
    pageSize: 10,
    pointNo: undefined,
    pointName: undefined,
    parentId: undefined,
    subjectId: undefined,
    params: {
    }
});

const loadTreeNode = async () => {
  try {
    treeNodes.value=[];
    queryParamsPoint.value.subjectId = form.value.subjectId;
    if(!queryParamsPoint.value.subjectId){
      return;
    }
    const res = await queryPointList(queryParamsPoint.value);
    treeNodes.value = proxy?.handleTree<PointVO>(res.data, 'pointId', 'parentId');
  } catch (error) {
    console.error('加载节点失败:', error);
  }
}
const subjectChange = async (value: number | string) => {
    versionOptions.value = [];
    //如果有变化,清空下拉列表中key、value
    form.value.versionId = '';
    teacherOptions.value = [];
    //如果有变化,清空下拉列表中key、value
    form.value.teacherId = '';
    if(value){
      initVersionOptions();
    }
    form.value.pointIdList = [];
    loadTreeNode();
};

const subjectOptions = ref<CourseSubjectVO[]>([]);
const initSubjectOptions= async () => {
    const query: CourseSubjectQuery = {
      pageNum: 1,
      pageSize: 10,
      subjectCode: undefined,
      subjectName: undefined,
      courseId:route.params.courseId,
      params: undefined
    };
    const rest = await listSubject(query);
    subjectOptions.value = rest.data;
}
const versionOptions = ref<CourseSubjectVersionVO[]>([]);
const initVersionOptions= async () => {
    const subjectId = form.value.subjectId;
    if(!subjectId){
      return;
    }
    const query: CourseSubjectQuery = {
      pageNum: 1,
      pageSize: 10,
      subjectId: subjectId,
      courseId:route.params.courseId,
      params: undefined
    };
    const rest = await listVersion(query);
    versionOptions.value = rest.data;
}

const versionChange = async (value: number | string) => {
    teacherOptions.value = [];
    //如果有变化,清空下拉列表中key、value
    form.value.teacherId = '';
    if(value){
      initTeacherOptions();
      initUnitOptions();
    }
};

const teacherOptions = ref<CourseSubjectTeacherVO[]>([]);

const initTeacherOptions= async () => {
    const subjectId = form.value.subjectId;
    if(!subjectId){
      return;
    }
    const versionId = form.value.versionId;
    if(!versionId){
      return;
    }
    const query: CourseSubjectTeacherQuery = {
      pageNum: 1,
      pageSize: 10,
      versionId: versionId,
      subjectId: subjectId,
      courseId:route.params.courseId,
      params: undefined
    };
    const rest = await listTeacher(query);
    teacherOptions.value = rest.data;
}


const unitOptions = ref<CourseSubjectUnitVO[]>([]);

const initUnitOptions= async () => {
    const subjectId = form.value.subjectId;
    if(!subjectId){
      return;
    }
    const versionId = form.value.versionId;
    if(!versionId){
      return;
    }
    const query: CourseSubjectUnitQuery = {
      pageNum: 1,
      pageSize: 10,
      versionId: versionId,
      subjectId: subjectId,
      courseId:route.params.courseId,
      params: undefined
    };
    const rest = await listUnit(query);
    unitOptions.value = rest.data;
}



//-------------------------搜索框条件初始化------------------------------------
const versionQueryOptions = ref<CourseSubjectVersionVO[]>([]);
const initVersionOptionsQuery= async () => {
    const query: CourseSubjectQuery = {
      pageNum: 1,
      pageSize: 10,
      courseId:route.params.courseId,
      params: undefined
    };
    const rest = await listVersion(query);
    versionQueryOptions.value = rest.data;
}

const versionQueryOptionsChange= async () => {
    initTeacherOptionsQuery();
    initUnitOptionsQuery();
}
const teacherQueryOptions = ref<CourseSubjectTeacherVO[]>([]);
const initTeacherOptionsQuery= async () => {
    queryParams.value.teacherId = null;
    const versionId = queryParams.value.versionId;
    if(!versionId){
      teacherQueryOptions.value=[];
      return;
    }
    const query: CourseSubjectTeacherQuery = {
      pageNum: 1,
      pageSize: 10,
      versionId: versionId,
      courseId:route.params.courseId,
      params: undefined
    };
    const rest = await listTeacher(query);
    teacherQueryOptions.value = rest.data;
}


const unitQueryOptions = ref<CourseSubjectUnitVO[]>([]);
const initUnitOptionsQuery= async () => {
    queryParams.value.unitId = null;
    const versionId = queryParams.value.versionId;
    if(!versionId){
      unitQueryOptions.value=[];
      return;
    }
    const query: CourseSubjectUnitQuery = {
      pageNum: 1,
      pageSize: 10,
      versionId: versionId,
      courseId:route.params.courseId,
      params: undefined
    };
    const rest = await listUnit(query);
    unitQueryOptions.value = rest.data;
}

//-------------------------搜索框条件初始化------------------------------------


onMounted(() => {
  courseId.value = route.params && (route.params.courseId as string | number);
  getList();
  getCourseInfo();
  initSubjectOptions();
  initVersionOptionsQuery();
});
</script>
<style scoped>
  .no-vertical-margin {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .profile-img {
    max-width: 70%;
    height: auto;
  }
</style>