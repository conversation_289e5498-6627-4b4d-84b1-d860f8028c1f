<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="类型名称" prop="configName">
              <el-input v-model="queryParams.configName" placeholder="请输入类型名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['pointsConfig:memberPointsConfig:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['pointsConfig:memberPointsConfig:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['pointsConfig:memberPointsConfig:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['pointsConfig:memberPointsConfig:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="memberPointsConfigList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="类型名称" align="center" prop="configName" />
        <el-table-column label="消耗积分数量" align="center" prop="pointsNum" />
        <el-table-column label="是否开通菁优" align="center" prop="jyeooStatus">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.jyeooStatus"/>
          </template>
        </el-table-column>
        <el-table-column label="期限" align="center" prop="configTerm" />
        <el-table-column label="期限类型" align="center" prop="configType">
          <template #default="scope">
            <dict-tag :options="sys_day_type" :value="scope.row.configType"/>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['pointsConfig:memberPointsConfig:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['pointsConfig:memberPointsConfig:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改会员类型积分设置对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body>
      <el-form ref="memberPointsConfigFormRef" :model="form" :rules="rules" label-width="130px">
        <el-form-item label="类型名称" prop="configName">
          <el-input v-model="form.configName" placeholder="请输入类型名称" />
        </el-form-item>
        <el-form-item label="消耗积分数量" prop="pointsNum">
          <el-input v-model="form.pointsNum" placeholder="请输入消耗积分数量" oninput="value = value.replace(/[^0-9\\d*]/g, '')"/>
        </el-form-item>
        <el-form-item label="期限" prop="configTerm">
          <el-input v-model="form.configTerm" placeholder="请输入期限" />
        </el-form-item>
        <el-form-item label="期限类型" prop="configType">
          <el-select v-model="form.configType" placeholder="请选择期限类型">
            <el-option
                v-for="dict in sys_day_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否开通菁优" prop="jyeooStatus">
          <el-radio-group v-model="form.jyeooStatus">
            <el-radio
              v-for="dict in sys_yes_no"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="顺序" prop="orderNum">
          <el-input v-model="form.orderNum" placeholder="请输入顺序" oninput="value = value.replace(/[^0-9\\d*]/g, '')"/>
        </el-form-item>
        <el-form-item label="积分图片" prop="configImage">
          <image-upload v-model="form.configImage"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MemberPointsConfig" lang="ts">
import { listMemberPointsConfig, getMemberPointsConfig, delMemberPointsConfig, addMemberPointsConfig, updateMemberPointsConfig } from '@/api/pointsConfig';
import { MemberPointsConfigVO, MemberPointsConfigQuery, MemberPointsConfigForm } from '@/api/pointsConfig/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_day_type,sys_yes_no } = toRefs<any>(proxy?.useDict('sys_day_type','sys_yes_no'));

const memberPointsConfigList = ref<MemberPointsConfigVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const memberPointsConfigFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: MemberPointsConfigForm = {
  configId: undefined,
  configName: undefined,
  pointsNum: undefined,
  configImage: undefined,
  configTerm: undefined,
  configType: undefined,
  jyeooStatus: undefined,
}
const data = reactive<PageData<MemberPointsConfigForm, MemberPointsConfigQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    configName: undefined,
    params: {
    }
  },
  rules: {
    configName: [
      { required: true, message: "类型名称不能为空", trigger: "blur" }
    ],
    pointsNum: [
      { required: true, message: "消耗积分数量不能为空", trigger: "blur" }
    ],
    configTerm: [
      { required: true, message: "期限不能为空", trigger: "blur" }
    ],
    jyeooStatus: [
      { required: true, message: "是否开通菁优不能为空", trigger: "change" }
    ],
    configType: [
      { required: true, message: "期限类型不能为空", trigger: "change" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询会员类型积分设置列表 */
const getList = async () => {
  loading.value = true;
  const res = await listMemberPointsConfig(queryParams.value);
  memberPointsConfigList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  memberPointsConfigFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: MemberPointsConfigVO[]) => {
  ids.value = selection.map(item => item.configId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加会员类型积分设置";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: MemberPointsConfigVO) => {
  reset();
  const _configId = row?.configId || ids.value[0]
  const res = await getMemberPointsConfig(_configId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改会员类型积分设置";
}

/** 提交按钮 */
const submitForm = () => {
  memberPointsConfigFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.configId) {
        await updateMemberPointsConfig(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addMemberPointsConfig(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: MemberPointsConfigVO) => {
  const _configIds = row?.configId || ids.value;
  await proxy?.$modal.confirm('是否确认删除会员类型积分设置编号为"' + _configIds + '"的数据项？').finally(() => loading.value = false);
  await delMemberPointsConfig(_configIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('pointsConfig/memberPointsConfig/export', {
    ...queryParams.value
  }, `memberPointsConfig_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
