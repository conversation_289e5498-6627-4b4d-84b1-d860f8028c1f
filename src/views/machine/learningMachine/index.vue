<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" >
            <el-row :gutter="15">
              <el-col :span="8">
                <el-form-item label="学区" prop="deptId">
                  <el-tree-select
                    v-model="queryParams.deptId"
                    :data="deptOptions"
                    :props="{ value: 'id', label: 'label', children: 'children' }"
                    value-key="id"
                    placeholder="请选择学区"
                    check-strictly
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="mac地址" prop="macAddr" label-width="100px">
                  <el-input v-model="queryParams.macAddr" placeholder="请输入mac地址" clearable @keyup.enter="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                 <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                  <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['machine:learningMachine:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['machine:learningMachine:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['machine:learningMachine:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['machine:learningMachine:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="learningMachineList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="学区" align="center" prop="deptName" />
        <el-table-column label="mac地址" align="center" prop="macAddr" />
        <el-table-column label="省份" align="center" prop="provinceName" />
        <el-table-column label="市编码" align="center" prop="cityName" />
        <el-table-column label="区" align="center" prop="districtName" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['machine:learningMachine:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['machine:learningMachine:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改学习机管理对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="learningMachineFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="学区" prop="deptId">
          <el-tree-select
            v-model="form.deptId"
            :data="deptOptions"
            :props="{ value: 'id', label: 'label', children: 'children' }"
            value-key="id"
            placeholder="请选择学区"
            check-strictly
            clearable
          />
        </el-form-item>
        <el-form-item label="mac地址" prop="macAddr">
          <el-input v-model="form.macAddr" placeholder="请输入mac地址" />
        </el-form-item>
        <el-form-item label="省份" prop="provinceCode">
           <el-select v-model="form.provinceCode" placeholder="请选择省" clearable @change="handleProvinceChange">
              <el-option v-for="province in provinceList" :key="province.code" :label="province.name"
                        :value="province.code"/>
            </el-select>
        </el-form-item>
        <el-form-item label="市编码" prop="cityCode">
          <el-select v-model="form.cityCode" placeholder="请选择市" clearable
            @change="handleCityChange">
              <el-option v-for="city in cityList" :key="city.code" :label="city.name"
                        :value="city.code"/>
            </el-select>
        </el-form-item>
        <el-form-item label="区" prop="districtCode">
           <el-select v-model="form.districtCode" placeholder="请选择区" clearable @change="handleDistrictChange">
              <el-option v-for="district in districtList" :key="district.code" :label="district.name"
                        :value="district.code"/>
            </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="LearningMachine" lang="ts">
import { listLearningMachine, getLearningMachine, delLearningMachine, addLearningMachine, updateLearningMachine } from '@/api/machine/learningMachine';
import { LearningMachineVO, LearningMachineQuery, LearningMachineForm } from '@/api/machine/learningMachine/types';
import { DeptVO } from '@/api/system/dept/types';
import api from '@/api/system/user';
import { listAreaF } from '@/api/system/area';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const learningMachineList = ref<LearningMachineVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const learningMachineFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: LearningMachineForm = {
  machineId: undefined,
  deptId: undefined,
  macAddr: undefined,
  addrStatus: undefined,
  provinceCode: undefined,
  cityCode: undefined,
  districtCode: undefined,
  status: undefined,
}
const data = reactive<PageData<LearningMachineForm, LearningMachineQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    macAddr: undefined,
    deptId: undefined,
    params: {
    }
  },
  rules: {
    deptId: [
      { required: true, message: "学区ID不能为空", trigger: "blur" }
    ],
    macAddr: [
      { required: true, message: "mac地址不能为空", trigger: "blur" }
    ],
    provinceCode: [
      { required: true, message: "省份不能为空", trigger: "blur" }
    ],
    cityCode: [
      { required: true, message: "市编码不能为空", trigger: "blur" }
    ],
    districtCode: [
      { required: true, message: "区不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

const deptOptions = ref<DeptVO[]>([]);

/** 查询学习机管理列表 */
const getList = async () => {
  loading.value = true;
  const res = await listLearningMachine(queryParams.value);
  learningMachineList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  learningMachineFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: LearningMachineVO[]) => {
  ids.value = selection.map(item => item.machineId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加学习机管理";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: LearningMachineVO) => {
  reset();
  const _machineId = row?.machineId || ids.value[0]
  const res = await getLearningMachine(_machineId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改学习机管理";
  getCityList();
  getDistrictList();
}

/** 提交按钮 */
const submitForm = () => {
  learningMachineFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.machineId) {
        await updateLearningMachine(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addLearningMachine(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: LearningMachineVO) => {
  const _machineIds = row?.machineId || ids.value;
  await proxy?.$modal.confirm('是否确认删除学习机管理编号为"' + _machineIds + '"的数据项？').finally(() => loading.value = false);
  await delLearningMachine(_machineIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('machine/learningMachine/export', {
    ...queryParams.value
  }, `learningMachine_${new Date().getTime()}.xlsx`)
}
const getTreeSelect = async () => {
  const res = await api.deptTreeSelect();
  deptOptions.value = res.data;
};

/** 省下拉列表 begin*/
const provinceList = ref([]); //
const getProvinceList = async () => {
  //查询省数据
  const query = {
    level: 1
  };
  const res = await listAreaF(query);
  provinceList.value = res.rows;
};
const getCityList = async () => {
  const value = form.value.provinceCode;
  if (value != undefined && value != '') {
    //查询省数据
    const query = {
      parentCode: value
    };
    const res = await listAreaF(query);
    cityList.value = res.rows;
  } else {
    //清除市下拉
    cityList.value = [];
    form.value.cityCode = '';
  }
};
const getDistrictList = async () => {
  const value = form.value.cityCode;
  if (value != undefined && value != '') {
    const query = {
      parentCode: value
    };
    const res = await listAreaF(query);
    districtList.value = res.rows;
  } else {
    districtList.value = [];
    form.value.districtCode = '';
  }
};


 /** 市下拉列表 begin*/
  const cityList = ref([]); //
  const handleProvinceChange = async (value: number | string) => {
    //清除市下拉
    cityList.value = [];
    form.value.cityCode = '';
    districtList.value = [];
    form.value.districtCode = '';
    if (value != undefined && value != '') {
      //查询省数据
      const query = {
        parentCode: value
      };
      const res = await listAreaF(query);
      cityList.value = res.rows;
    }

  };
 /** 市下拉列表 begin*/
const districtList = ref([]); //
const handleCityChange = async (value: number | string) => {
  if (value != undefined && value != '') {
    //查询省数据
    const query = {
      parentCode: value
    };
    const res = await listAreaF(query);
    districtList.value = res.rows;
  } else {
    //清除市下拉
    districtList.value = [];
  }
};
onMounted(() => {
  getList();
  getTreeSelect();
  getProvinceList();
});
</script>
