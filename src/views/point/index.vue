<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="考点编号" prop="pointNo">
              <el-input v-model="queryParams.pointNo" placeholder="请输入考点编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="学科名称" prop="pointName">
              <el-input v-model="queryParams.pointName" placeholder="请输入学科名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['point:point:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['point:point:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['point:point:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['point:point:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="pointList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键ID" align="center" prop="pointId" v-if="true" />
        <el-table-column label="考点编号" align="center" prop="pointNo" />
        <el-table-column label="学科名称" align="center" prop="pointName" />
        <el-table-column label="科目主键ID" align="center" prop="subjectId" />
        <el-table-column label="父考点id" align="center" prop="parentId" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['point:point:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['point:point:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改考点管理对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="pointFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="考点编号" prop="pointNo">
          <el-input v-model="form.pointNo" placeholder="请输入考点编号" />
        </el-form-item>
        <el-form-item label="学科名称" prop="pointName">
          <el-input v-model="form.pointName" placeholder="请输入学科名称" />
        </el-form-item>
        <el-form-item label="描述" prop="pointDesc">
            <el-input v-model="form.pointDesc" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="科目主键ID" prop="subjectId">
          <el-input v-model="form.subjectId" placeholder="请输入科目主键ID" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Point" lang="ts">
import { listPoint, getPoint, delPoint, addPoint, updatePoint } from '@/api/point';
import { PointVO, PointQuery, PointForm } from '@/api/point/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const pointList = ref<PointVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const pointFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: PointForm = {
  pointId: undefined,
  pointNo: undefined,
  pointName: undefined,
  pointDesc: undefined,
  subjectId: undefined,
  parentId: undefined,
}
const data = reactive<PageData<PointForm, PointQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    pointNo: undefined,
    pointName: undefined,
    parentId: undefined,
    params: {
    }
  },
  rules: {
    pointName: [
      { required: true, message: "学科名称不能为空", trigger: "blur" }
    ],
    parentId: [
      { required: true, message: "父考点id不能为空", trigger: "change" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询考点管理列表 */
const getList = async () => {
  loading.value = true;
  const res = await listPoint(queryParams.value);
  pointList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  pointFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: PointVO[]) => {
  ids.value = selection.map(item => item.pointId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加考点管理";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: PointVO) => {
  reset();
  const _pointId = row?.pointId || ids.value[0]
  const res = await getPoint(_pointId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改考点管理";
}

/** 提交按钮 */
const submitForm = () => {
  pointFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.pointId) {
        await updatePoint(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addPoint(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: PointVO) => {
  const _pointIds = row?.pointId || ids.value;
  await proxy?.$modal.confirm('是否确认删除考点管理编号为"' + _pointIds + '"的数据项？').finally(() => loading.value = false);
  await delPoint(_pointIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('point/point/export', {
    ...queryParams.value
  }, `point_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
