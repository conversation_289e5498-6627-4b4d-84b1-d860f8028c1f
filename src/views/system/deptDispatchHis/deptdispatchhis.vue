<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="目标部门" prop="targetDeptId">
              <el-tree-select
                v-model="queryParams.targetDeptId"
                :data="deptOptions"
                :props="{ value: 'id', label: 'label', children: 'children' }"
                value-key="id"
                placeholder="请选择目标部门"
                check-strictly
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <el-table v-loading="loading" :data="deptDispatchHisList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="目标部门" align="center" prop="targetDeptName" />
        <el-table-column label="分配数量" align="center" prop="changeCount" />
        <el-table-column label="原有总数量" align="center" prop="originCount" />
        <el-table-column label="变化后总数量" align="center" prop="targetCount" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改账号分配历史对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="deptDispatchHisFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="部门id" prop="deptId">
          <el-input v-model="form.deptId" placeholder="请输入部门id" />
        </el-form-item>
        <el-form-item label="用户id" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户id" />
        </el-form-item>
        <el-form-item label="目标部门id" prop="targetDeptId">
          <el-input v-model="form.targetDeptId" placeholder="请输入目标部门id" />
        </el-form-item>
        <el-form-item label="变化数量" prop="changeCount">
          <el-input v-model="form.changeCount" placeholder="请输入变化数量" />
        </el-form-item>
        <el-form-item label="原有总数量" prop="originCount">
          <el-input v-model="form.originCount" placeholder="请输入原有总数量" />
        </el-form-item>
        <el-form-item label="变化后总数量" prop="targetCount">
          <el-input v-model="form.targetCount" placeholder="请输入变化后总数量" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="DeptDispatchHis" lang="ts">
import { listDeptDispatchHis, getDeptDispatchHis, delDeptDispatchHis, addDeptDispatchHis, updateDeptDispatchHis } from '@/api/system/deptDispatchHis';
import { DeptDispatchHisVO, DeptDispatchHisQuery, DeptDispatchHisForm } from '@/api/system/deptDispatchHis/types';
import { propTypes } from '@/utils/propTypes';
import api from '@/api/system/user';
import { DeptVO } from '@/api/system/dept/types';


const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const deptDispatchHisList = ref<DeptDispatchHisVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const deptDispatchHisFormRef = ref<ElFormInstance>();
const deptOptions = ref<DeptVO[]>([]);

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: DeptDispatchHisForm = {
  hisId: undefined,
  deptId: undefined,
  userId: undefined,
  targetDeptId: undefined,
  changeCount: undefined,
  originCount: undefined,
  targetCount: undefined,
}
const data = reactive<PageData<DeptDispatchHisForm, DeptDispatchHisQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    deptId: undefined,
    userId: undefined,
    targetDeptId: undefined,
    changeCount: undefined,
    originCount: undefined,
    targetCount: undefined,
    params: {
    }
  },
  rules: {
    hisId: [
      { required: true, message: "主键不能为空", trigger: "blur" }
    ],
    deptId: [
      { required: true, message: "部门id不能为空", trigger: "blur" }
    ],
    userId: [
      { required: true, message: "用户id不能为空", trigger: "blur" }
    ],
    targetDeptId: [
      { required: true, message: "目标部门id不能为空", trigger: "blur" }
    ],
    changeCount: [
      { required: true, message: "变化数量不能为空", trigger: "blur" }
    ],
    originCount: [
      { required: true, message: "原有总数量不能为空", trigger: "blur" }
    ],
    targetCount: [
      { required: true, message: "变化后总数量不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

const props = defineProps({
  param: propTypes.any.isRequired
});
/** 查询账号分配历史列表 */
const getList = async () => {
  loading.value = true;
  const deptAuthId = computed(() => props.param);
  queryParams.value.deptId = deptAuthId.value;
  const res = await listDeptDispatchHis(queryParams.value);
  deptDispatchHisList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  deptDispatchHisFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: DeptDispatchHisVO[]) => {
  ids.value = selection.map(item => item.hisId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加账号分配历史";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: DeptDispatchHisVO) => {
  reset();
  const _hisId = row?.hisId || ids.value[0]
  const res = await getDeptDispatchHis(_hisId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改账号分配历史";
}

/** 提交按钮 */
const submitForm = () => {
  deptDispatchHisFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.hisId) {
        await updateDeptDispatchHis(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addDeptDispatchHis(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作  */
const handleDelete = async (row?: DeptDispatchHisVO) => {
  const _hisIds = row?.hisId || ids.value;
  await proxy?.$modal.confirm('是否确认删除账号分配历史编号为"' + _hisIds + '"的数据项？').finally(() => loading.value = false);
  await delDeptDispatchHis(_hisIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('system/deptDispatchHis/export', {
    ...queryParams.value
  }, `deptDispatchHis_${new Date().getTime()}.xlsx`)
}
const getTreeSelect = async () => {
  const res = await api.deptTreeSelect();
  deptOptions.value = res.data;
};

onMounted(() => {
  getTreeSelect(); // 初始化部门数据
  getList();
});
</script>
