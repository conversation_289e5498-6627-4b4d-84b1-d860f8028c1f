<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
             <el-form-item label="部门" prop="deptId">
              <el-tree-select
                v-model="queryParams.deptId"
                :data="deptOptions"
                :props="{ value: 'id', label: 'label', children: 'children' }"
                value-key="id"
                placeholder="请选择部门"
                check-strictly
                clearable
              />
            </el-form-item>
            <el-form-item label="会员姓名" prop="nickName">
              <el-input v-model="queryParams.nickName" placeholder="请输入会员姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:deptConsumeHis:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['system:deptConsumeHis:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['system:deptConsumeHis:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:deptConsumeHis:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="deptConsumeHisList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="变化数量" align="center" prop="changeCount" />
        <el-table-column label="原有总数量" align="center" prop="originCount" />
        <el-table-column label="变化后总数量" align="center" prop="targetCount" />
        <el-table-column label="会员姓名" align="center" prop="nickName" />
        <el-table-column label="会员账号" align="center" prop="userName" />
        <el-table-column label="授权类型" align="center" prop="configName" />
        <el-table-column label="创建时间" align="center" prop="createTime" />
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改学校账号消耗记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="deptConsumeHisFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户id(学生id)" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户id(学生id)" />
        </el-form-item>
        <el-form-item label="部门id(大部分是学校)" prop="deptId">
          <el-input v-model="form.deptId" placeholder="请输入部门id(大部分是学校)" />
        </el-form-item>
        <el-form-item label="变化数量" prop="changeCount">
          <el-input v-model="form.changeCount" placeholder="请输入变化数量" />
        </el-form-item>
        <el-form-item label="原有总数量" prop="originCount">
          <el-input v-model="form.originCount" placeholder="请输入原有总数量" />
        </el-form-item>
        <el-form-item label="会员姓名" prop="nickName">
          <el-input v-model="form.nickName" placeholder="请输入会员姓名" />
        </el-form-item>
        <el-form-item label="会员账号" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入会员账号" />
        </el-form-item>
        <el-form-item label="变化后总数量" prop="targetCount">
          <el-input v-model="form.targetCount" placeholder="请输入变化后总数量" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="DeptConsumeHis" lang="ts">
import { listDeptConsumeHis, getDeptConsumeHis, delDeptConsumeHis, addDeptConsumeHis, updateDeptConsumeHis } from '@/api/system/deptConsumeHis';
import { DeptConsumeHisVO, DeptConsumeHisQuery, DeptConsumeHisForm } from '@/api/system/deptConsumeHis/types';
import { propTypes } from '@/utils/propTypes';
import api from '@/api/system/user';
import { DeptVO } from '@/api/system/dept/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const deptConsumeHisList = ref<DeptConsumeHisVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const deptConsumeHisFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: DeptConsumeHisForm = {
  hisId: undefined,
  userId: undefined,
  deptId: undefined,
  changeCount: undefined,
  originCount: undefined,
  nickName: undefined,
  userName: undefined,
  targetCount: undefined,
  authType: undefined,
}
const data = reactive<PageData<DeptConsumeHisForm, DeptConsumeHisQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userId: undefined,
    deptId: undefined,
    changeCount: undefined,
    originCount: undefined,
    nickName: undefined,
    userName: undefined,
    targetCount: undefined,
    authType: undefined,
    params: {
    }
  },
  rules: {
    hisId: [
      { required: true, message: "主键不能为空", trigger: "blur" }
    ],
    userId: [
      { required: true, message: "用户id(学生id)不能为空", trigger: "blur" }
    ],
    deptId: [
      { required: true, message: "部门id(大部分是学校)不能为空", trigger: "blur" }
    ],
    changeCount: [
      { required: true, message: "变化数量不能为空", trigger: "blur" }
    ],
    originCount: [
      { required: true, message: "原有总数量不能为空", trigger: "blur" }
    ],
    nickName: [
      { required: true, message: "会员姓名不能为空", trigger: "blur" }
    ],
    userName: [
      { required: true, message: "会员账号不能为空", trigger: "blur" }
    ],
    targetCount: [
      { required: true, message: "变化后总数量不能为空", trigger: "blur" }
    ],
    authType: [
      { required: true, message: "授权类型来自ci_member_points_config表不能为空", trigger: "change" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

const props = defineProps({
  param: propTypes.any.isRequired
});

/** 查询学校账号消耗记录列表 */
const getList = async () => {
  loading.value = true;
  if(!queryParams.value.deptId){
    const deptAuthId = computed(() => props.param);
    queryParams.value.deptId = deptAuthId.value;
  }
  const res = await listDeptConsumeHis(queryParams.value);
  deptConsumeHisList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  deptConsumeHisFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: DeptConsumeHisVO[]) => {
  ids.value = selection.map(item => item.hisId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加学校账号消耗记录";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: DeptConsumeHisVO) => {
  reset();
  const _hisId = row?.hisId || ids.value[0]
  const res = await getDeptConsumeHis(_hisId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改学校账号消耗记录";
}

/** 提交按钮 */
const submitForm = () => {
  deptConsumeHisFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.hisId) {
        await updateDeptConsumeHis(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addDeptConsumeHis(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: DeptConsumeHisVO) => {
  const _hisIds = row?.hisId || ids.value;
  await proxy?.$modal.confirm('是否确认删除学校账号消耗记录编号为"' + _hisIds + '"的数据项？').finally(() => loading.value = false);
  await delDeptConsumeHis(_hisIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('system/deptConsumeHis/export', {
    ...queryParams.value
  }, `deptConsumeHis_${new Date().getTime()}.xlsx`)
}
const deptOptions = ref<DeptVO[]>([]);
const getTreeSelect = async () => {
  const res = await api.deptTreeSelect();
  deptOptions.value = res.data;
};
onMounted(() => {
  getTreeSelect();
  getList();
});
</script>
