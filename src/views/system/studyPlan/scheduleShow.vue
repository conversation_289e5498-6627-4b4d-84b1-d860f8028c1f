<template>
  <div class="p-2">
    <el-row>
      <el-col :span="17" :offset="0.2" style="padding: 10px;">
        <h3>
          <el-date-picker
            v-model="selectedMonthStr"
            type="month"
            format="YYYY-MM"
            value-format="YYYY-MM"
            placeholder="选择年月"
            @change="generateCalendar"
            style="width:120px;"
          />
        </h3>
        <table>
          <thead>
            <tr>
              <th v-for="day in daysOfWeek" :key="day">{{ day }}</th>
            </tr>
          </thead>
          <tbody v-loading="loading">
            <tr v-for="(week, weekIndex) in calendar" :key="weekIndex">
              <td style="width:300px;"
                v-for="(day, dayIndex) in week"
                :key="dayIndex"
                :data-date="day?.date"
                :class="{
                  'has-event': day?.event,
                  'current-month': day?.isCurrentMonth,
                }"
              >
                <el-row>
                  <el-col style="border: none !important;">
                    <span v-if="day">{{ day.day }}</span>
                  </el-col>
                </el-row>
                <el-row v-if="day?.plans" v-for="(plan, planIndex) in day.plans" justify="center">
                  <el-col :span="23" style="border: none !important;">
                    <el-tooltip class="ellipsis-text" :content="plan.planShowStr" placement="top">
                      <span class="small-gray ellipsis-text">{{ plan.planShowStrShort }} </span>
                    </el-tooltip>
                  </el-col>
                </el-row>
              </td>
            </tr>
          </tbody>
        </table>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="ScheduleShow" lang="ts">
import { PlanDayQuery } from '@/api/study/studyPlan/types';
import { queryPlanDay } from '@/api/study/studyPlan';
import {formatDateToStr} from '@/utils';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const route = useRoute();

const loading = ref(false);

const selectedMonthStr = ref(formatDateToStr(new Date()).slice(0, 7));
const daysOfWeek = ref<string []>(["周一", "周二", "周三", "周四", "周五", "周六", "周日"]);
 
const calendar = ref([]);
 
const props = defineProps({
  param: { type: null, required: true }
});
const userId = computed(() => props.param);

const generateCalendar =  async () => {
    loading.value = true;
    let param:PlanDayQuery={
      yearMonth:selectedMonthStr.value,
      userIds: userId.value
    }
    const result = await queryPlanDay(param);
    calendar.value = result.data;
    loading.value = false;
};

onMounted(() => {
  generateCalendar();
});
</script>
<style scoped>
.content {
  margin-left:10px;
  margin-top:10px;
  margin-bottom:10px;
  text-align: center;
}
.content-left {
  margin-left:5px;
  margin-top:10px;
  margin-bottom:10px;
  text-align: left;
}


/* 全局覆盖 el-col 边框 */
.el-col {
  border: 1px solid #e4e7ed !important;
}
/* 全局覆盖 el-col 边框 */
.el-col-noborder {
  border: none !important;
}
.typename {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  color: #333;
  background-color: transparent;
}
/* 点击后的激活样式 */
.typename.active {
  color: #fff;
  background-color: #409EFF; /* Element Plus 蓝色 */
}
.small-gray {
    font-size: 12px;
    color: #666;
}

.container {
  display: flex;
  padding: 20px;
}
 
.course-list {
  width: 200px;
  margin-right: 20px;
}
 
.course-item {
  padding: 10px;
  margin: 5px 0;
  background-color: #f0f0f0;
  cursor: grab;
}
 
.calendar {
  flex: 1;
}
 
table {
  width: 100%;
  border-collapse: collapse;
}
 
th,
td {
  border: 1px solid #ccc;
  text-align: center;
  padding: 10px;
}
 
td.current-month {
  background-color: #fff;
}
 
td.has-event {
  background-color: #d1e7dd;
}
 
.event {
  display: block;
  font-size: 12px;
  color: #007bff;
}
/* 文本溢出显示省略号的核心样式 */
.ellipsis-text{
  display: inline-block; /* 或者 block，取决于布局需求 */
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 超出部分显示省略号 */
  /* 下面是可选的样式设置 */
  padding: 5px; /* 可选：增加内边距 */
  box-sizing: border-box; /* 确保padding不会影响总宽度 */
}
.cursor-point {
  cursor: pointer;
  font-size: 12px;
  color: #666;
}
.footer-buttons {
  position: fixed; /* 固定定位 */
  bottom: 0; /* 距离底部为 0 */
  left: 0;
  right: 0;
  background-color: #fff; /* 背景颜色，避免遮挡内容 */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
  padding: 10px 20px; /* 内边距 */
  z-index: 1000; /* 确保按钮层在最上层 */
}
</style>
