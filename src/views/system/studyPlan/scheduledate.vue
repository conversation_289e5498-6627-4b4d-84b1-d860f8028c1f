<template>
  <div class="p-2">
    <el-row>
      <el-col :span="5" :offset="0.2">
        <div class="content-left">
          <span>已选课程</span>
        </div>
          <div>
            <div class="content-left" v-if="courseVOListSelected" v-for="course in courseVOListSelected??[]">
                <span> {{ course.courseName }}</span>
                <draggable
                  :list="course.resourceVoList"
                  :group="{ name: 'courses', pull: 'clone', put: false }"
                  :sort="false"
                  item-key="resource_id">
                    <template #item="{ element }">
                      <div class="course-item content-left" style="margin-left:20px;margin-right:5px;"  draggable="true"
                      @dragstart="onDragStart($event, element)">
                        <span> {{ element.videoResourceName }}</span>
                          <el-row>
                            <el-col :span="10" :offset="1" style="border: none !important;">
                              <span class="small-gray">科目:{{element.courseSubject}}</span>
                            </el-col>
                            <el-col :span="10" style="border: none !important;">
                              <span class="small-gray">版本:{{element.courseVersion}}</span>
                            </el-col>
                          </el-row>
                          <el-row>
                            <el-col :span="10" :offset="1" style="border: none !important;">
                              <span class="small-gray">名师:{{element.courseTeacher}}</span>
                            </el-col>
                            <el-col :span="10" style="border: none !important;">
                              <span class="small-gray">大纲:{{element.courseSyllabus}}</span>
                            </el-col>
                          </el-row>
                        </div>
                    </template>
                  </draggable>
            </div>
        </div>
      </el-col>
      <el-col :span="17" :offset="0.2" style="padding: 10px;">
        <h3>
          <el-date-picker
            v-model="selectedMonthStr"
            type="month"
            format="YYYY-MM"
            value-format="YYYY-MM"
            placeholder="选择年月"
            @change="generateCalendar"
            style="width:120px;"
          />
        </h3>
        <table>
          <thead>
            <tr>
              <th v-for="day in daysOfWeek" :key="day">{{ day }}</th>
            </tr>
          </thead>
          <tbody v-loading="loading">
            <tr v-for="(week, weekIndex) in calendar" :key="weekIndex">
              <td
                v-for="(day, dayIndex) in week"
                :key="dayIndex"
                :data-date="day?.date"
                :class="{
                  'has-event': day?.event,
                  'current-month': day?.isCurrentMonth,
                }"
                @dragover.prevent
                @drop="handleDrop($event, day)"
              >
                <el-row>
                  <el-col style="border: none !important;">
                    <span v-if="day">{{ day.day }}</span>
                  </el-col>
                </el-row>
                <el-row v-if="day?.plans" v-for="(plan, planIndex) in day.plans" justify="center">
                  <el-col :span="5" style="border: none !important;">
                    <el-tooltip class="ellipsis-text" :content="plan.planShowStr" placement="top">
                      <span class="small-gray ellipsis-text">{{ plan.planShowStrShort }} </span>
                    </el-tooltip>
                  </el-col>
                  <el-col :span="5" :offset="1" style="border: none !important;">
                    <span class="small-gray ellipsis-text cursor-point" style="margin-left:15px" @click="removeScheduleClick(weekIndex,dayIndex,planIndex)">删除</span>
                  </el-col>
                </el-row>
              </td>
            </tr>
          </tbody>
        </table>
      </el-col>
    </el-row>
    <el-row class="row-bg footer-buttons">
        <el-col :span="11" class="el-col-noborder">
          <div class="grid-content ep-bg-purple" >
          </div>
        </el-col>
        <el-col :span="3"  class="el-col-noborder">
          <div class="grid-content ep-bg-purple-light button-container" >
            <el-button :loading="buttonLoading" type="primary" @click="submitFormSchedule">保存</el-button>
            <el-button @click="handleClose">   关闭   </el-button>
          </div>
        </el-col  class="el-col-noborder">
        <el-col :span="9"  class="el-col-noborder"><div class="grid-content ep-bg-purple" ></div>
        </el-col>
      </el-row>

     <!-- 添加或修改用户信息对话框 -->
     <el-dialog v-model="dialog.visible" width="700px" append-to-body>
      <el-form label-width="120px" ref="scheduleFormRef" :title="dialog.title" :model="form" :rules="rules" >
        <el-form-item label="学习内容：">
          <span> {{ form.videoResourceName}}</span>
        </el-form-item>
        <el-form-item label="日期：">
          <span> {{ form.planDate}}</span>
        </el-form-item>
        <el-row>
          <el-col :span="10" style="border: none !important;">
            <el-form-item label="开始时间" prop="startTime">
              <el-time-picker
                  v-model="form.startTime"
                  value-format="HH:mm"
                  format="HH:mm"
                  placeholder="请选择时间"
                />
            </el-form-item>
          </el-col>
          <el-col :span="10" style="border: none !important;">
            <el-form-item label="结束时间" prop="endTime">
              <el-time-picker
                  v-model="form.endTime"
                  value-format="HH:mm"
                  format="HH:mm"
                  placeholder="请选择时间"
                />
            </el-form-item>
          </el-col>
        </el-row>
        
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="StudyPlan" lang="ts">
import { CourseVO } from '@/api/course/course/types';
import { PlanDayQuery } from '@/api/study/studyPlan/types';
import { addStudyPlanSchedule,queryPlanDay } from '@/api/study/studyPlan';
import courseScheduleStore from '@/store/modules/courseSchedule';
import draggable from "vuedraggable";
import {formatDateToStr} from '@/utils';
import { RouteLocationNormalized } from 'vue-router';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const { sys_course_subject } = toRefs<any>(proxy?.useDict('sys_course_subject'));

const activeTab = ref('single');

const buttonLoading = ref(false);

const route = useRoute();

const loading = ref(false);

const courseVOListSelected = ref<CourseVO[]>([]);

const scheduleFormRef = ref<ElFormInstance>();

export interface ScheduleDateForm{
  /**
   * 主键ID
   */
  resourceId?: string | number;

  /**
   * 课程id
   */
  courseId?: string | number;

  /**
   * 视频资源名称
   */
  videoResourceName?: string | number;

  /**
   * 科目
   */
  courseSubject?: string;

  /**
   * 版本 来源字典表 苏教版、人教版
   */
  courseVersion?: string;

  /**
   * 名师
   */
  courseTeacher?: string;

  /**
   * 大纲 来源于字典表
   */
  courseSyllabus?: string;

  /**
   * 开始日期
   */
  startTime?: string;

  /**
   * 结束日期
   */
  endTime?: string;
  /**
   * 日期
   */
  planDate?: string;

  planShowStr?: string;

  planShowStrShort?: string;
}

const dialog = reactive<DialogOption>({
  visible: false,
  title: '学习规划设置'
});

const form = ref<ScheduleDateForm>({
  startTime: undefined,
  endTime: undefined,
});
const validateEndTime = (rule, value, callback) => {
  if (value !== undefined && form.value.startTime >= form.value.endTime) {
    callback(new Error('开始时间不能大于结束时间'));
  } else {
    callback();
  }
};

const rules = ref({
  startTime: [
    { required: true, message: "开始时间不能为空", trigger: "blur" },
    { validator: validateEndTime, trigger: 'blur' }
  ],
  endTime: [
    { required: true, message: "结束时间不能为空", trigger: "blur" }
  ]
});

const months = ref([
      { value: 0, label: "一月" },
      { value: 1, label: "二月" },
      { value: 2, label: "三月" },
      { value: 3, label: "四月" },
      { value: 4, label: "五月" },
      { value: 5, label: "六月" },
      { value: 6, label: "七月" },
      { value: 7, label: "八月" },
      { value: 8, label: "九月" },
      { value: 9, label: "十月" },
      { value: 10, label: "十一月" },
      { value: 11, label: "十二月" },
    ]);
 
    const selectedMonthStr = ref(formatDateToStr(new Date()).slice(0, 7));
    const daysOfWeek = ref<string []>(["周一", "周二", "周三", "周四", "周五", "周六", "周日"]);
 
    const calendar = ref([]);
 
    const generateCalendar =  async () => {
      loading.value = true;
      let param:PlanDayQuery={
        yearMonth:selectedMonthStr.value,
        userIds: route.query.userIds
      }
      const result = await queryPlanDay(param);
      calendar.value = result.data;
      console.log(calendar.value);
      loading.value = false;
  };

  const onDragStart = (event, resource) => {
    form.value = {...resource};
   // Object.assign(form.value, {...resource}); // 通过属性合并更新对象
    event.dataTransfer.setData("text/plain", resource.resourceId);
  };
  const dayRef = ref();
  const handleDrop = (event, day) => {
    form.value.planDate = formatDateToStr(new Date(selectedMonthStr.value+"-"+day.day));
    console.log(form.value.planDate);
    debugger
    dayRef.value  = null;
    dayRef.value = day;
    dialog.visible = true;
  };
  const setDay = ()=>{
    scheduleFormRef.value?.validate(async (valid: boolean) => {
      if (valid) {
        dayRef.value.event = "";
        if(!dayRef.value.plans){
          dayRef.value.plans = [];
        }
        let temp = {...form.value};
        temp.planShowStr =form.value.startTime;//+"-"+form.value.endTime
        if(temp.courseSubject){
          temp.planShowStr = temp.planShowStr+"  "+temp.courseSubject;
        }
        if(temp.planShowStr.length>7){
          temp.planShowStrShort = temp.planShowStr.substr(0, 8)+"...";
        }else{
          temp.planShowStrShort = temp.planShowStr;
        }
        
        dayRef.value.plans.push({...temp});

        dialog.visible = false;
        /** 提交按钮 
          * 
            *  const foundCourse = courseVOListSelected.value.find(
            (course) => course.courseId === form.value.courseId
        );
        */
      }
    });
  }
    /** 提交按钮 */
  const submitForm = () => {
    setDay();
  }
 

/** 取消按钮 */
const cancel = () => {
  dialog.visible = false;
}
const removeScheduleClick = (weekIndex,dayIndex,planIndex)=>{
  calendar.value[weekIndex][dayIndex].plans.splice(planIndex, 1);
}
const handleClose = () => {
  const obj: RouteLocationNormalized = {
    fullPath: '',
    hash: '',
    matched: [],
    meta: undefined,
    name: undefined,
    params: undefined,
    redirectedFrom: undefined,
    path: '/system/studyPlan',
    query: { t: Date.now().toString()}
  };
  proxy?.$tab.closeOpenPage(obj);
};
// 声明类型
interface FormData {
  userIds?: any; // 适配路由查询参数类型
  schdules?: any[];
  yearMonth?: string;
}

// 初始化对象并赋值
const submitFormSchedule= async()=>{
  const formData: FormData = {};
  formData.userIds = route.query.userIds;
  formData.schdules = [];
  formData.yearMonth = selectedMonthStr.value;
  calendar.value.forEach((week, weekIndex) => {
    week.forEach((day, dayIndex) => {
      if(day.plans){
        day.plans.forEach((plan, eventIndex) => {
          formData.schdules.push({
            planDate:plan.planDate,
            courseId:plan.courseId,
            resourceId:plan.resourceId,
            startTime:plan.startTime,
            endTime:plan.endTime
          });
        });
      }
    });
  });
  buttonLoading.value = true;;
  await addStudyPlanSchedule(formData).finally(() =>  buttonLoading.value = false);
  proxy?.$modal.msgSuccess("操作成功");
  generateCalendar();
}


onMounted(() => {
  courseScheduleStore();
  const courses = courseScheduleStore().readAll();
  courseVOListSelected.value = courses;
  generateCalendar();
});
</script>
<style scoped>
.content {
  margin-left:10px;
  margin-top:10px;
  margin-bottom:10px;
  text-align: center;
}
.content-left {
  margin-left:5px;
  margin-top:10px;
  margin-bottom:10px;
  text-align: left;
}

.el-row-magin {
  margin-bottom: 10px; /* 下边距 */
  margin-left: 3px;
}

/* 全局覆盖 el-col 边框 */
.el-col {
  border: 1px solid #e4e7ed !important;
}
/* 全局覆盖 el-col 边框 */
.el-col-noborder {
  border: none !important;
}
.typename {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  color: #333;
  background-color: transparent;
}
/* 点击后的激活样式 */
.typename.active {
  color: #fff;
  background-color: #409EFF; /* Element Plus 蓝色 */
}
.small-gray {
    font-size: 12px;
    color: #666;
}

.container {
  display: flex;
  padding: 20px;
}
 
.course-list {
  width: 200px;
  margin-right: 20px;
}
 
.course-item {
  padding: 10px;
  margin: 5px 0;
  background-color: #f0f0f0;
  cursor: grab;
}
 
.calendar {
  flex: 1;
}
 
table {
  width: 100%;
  border-collapse: collapse;
}
 
th,
td {
  border: 1px solid #ccc;
  text-align: center;
  padding: 10px;
}
 
td.current-month {
  background-color: #fff;
}
 
td.has-event {
  background-color: #d1e7dd;
}
 
.event {
  display: block;
  font-size: 12px;
  color: #007bff;
}
/* 文本溢出显示省略号的核心样式 */
.ellipsis-text{
  display: inline-block; /* 或者 block，取决于布局需求 */
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 超出部分显示省略号 */
  /* 下面是可选的样式设置 */
  padding: 5px; /* 可选：增加内边距 */
  box-sizing: border-box; /* 确保padding不会影响总宽度 */
}
.cursor-point {
  cursor: pointer;
  font-size: 12px;
  color: #666;
}
.footer-buttons {
  position: fixed; /* 固定定位 */
  bottom: 0; /* 距离底部为 0 */
  left: 0;
  right: 0;
  background-color: #fff; /* 背景颜色，避免遮挡内容 */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
  padding: 10px 20px; /* 内边距 */
  z-index: 1000; /* 确保按钮层在最上层 */
}
</style>
