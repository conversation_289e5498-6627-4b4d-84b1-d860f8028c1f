<template>
  <div class="member-detail">
    <div class="header-cards">
      <!-- Left Card: Member Info -->
      <el-card class="member-info-card" :body-style="{ padding: '24px' }">
        <div class="subject-info">
          <div class="subject-scope">
            <span class="label">科目观看范围：</span>
            <el-link type="primary" @click="showSubjects">高中({{userinfo.subjectSum}})科</el-link>
            <span class="separator" style="display: none">|</span>
            <el-link type="primary" style="display: none">初中(0)科</el-link>
          </div>
        </div>
        <div class="member-info">
          <div class="avatar-info">
            <el-avatar :size="80" :src="graduationAvatar" class="profile-avatar" />
            <div class="basic-info">
              <div class="name-row">
                <div class="name-section">
                  <h2> {{ userinfo.nickName}}</h2>
                  <el-tag size="small" class="member-tag" effect="plain">
                    <el-icon><UserFilled /></el-icon>
                    {{ userinfo.memberTypeName }}
                  </el-tag>
                </div>
                <div class="info-row">
                  <span>{{ userinfo.userName}}</span>
                  <el-divider direction="vertical" />
                  <dict-tag v-if="userinfo.grade !== null && userinfo.grade !== undefined" :options="sys_member_grade" :value="userinfo.grade">
                  </dict-tag>
                  <el-divider direction="vertical" />
                </div>
              </div>
              <div class="tutor-info">
                <div class="info-item">
                  <span class="label">伴学师：</span>
                  <span> {{ userinfo.studyMentorNickName}}</span>
                  <el-icon class="verified"><CircleCheck /></el-icon>
                </div>
                <div class="info-item">
                  <span class="label">来源：</span>
                  <dict-tag v-if="userinfo.source !== null && userinfo.source !== undefined" :options="sys_member_source" :value="userinfo.source">
                  </dict-tag>
                </div>
                <div class="info-item">
                  <span class="label">家长电话：</span>
                  <span>{{ userinfo.parentPhoneFirst}}</span>
                  <span class="label" style="margin-left: 12px;display: none">绑定家长：</span>
                  <span class="pending" style="display: none">未绑定</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- Right Card: Subject Info -->
      <el-card class="subject-info-card" :body-style="{ padding: '24px' }">
        <div class="subject-info">
          <div class="top-right">
            <el-button class="member-card" type="primary" text>
              <el-icon><Ticket /></el-icon>
              会员卡
            </el-button>
            <el-link class="view-all" type="primary" style="display:none">查看全部(0) ></el-link>
          </div>
          <div>
            <image-show v-if="userinfo.memberImg" :src="userinfo.memberImg" :width="350" :height="170"/>
          </div>
        </div>
      </el-card>
    </div>

    <!-- Navigation Tabs -->
    <div class="nav-tabs">
      <el-tabs v-model="activeTab" class="member-tabs">
        <el-tab-pane label="会员档案" name="profile">
            <memberUpdate :param="tabParam" @call-refash-member="refashMember"/>
        </el-tab-pane>

        <el-tab-pane label="学习计划" name="schedule" >
            <ScheduleShow v-if="userId && activeTab=='schedule'" :param="userId"/>
        </el-tab-pane>
        <el-tab-pane label="错题本" name="mistakes">
            <MistakeNote v-if="userId && activeTab=='mistakes'"  :param="userId"/>
        </el-tab-pane>
        <el-tab-pane label="学习记录" name="studyRecord">
            <StudyRecord v-if="userId && activeTab=='studyRecord'" :param="userId"/>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>

  <!-- Subject Dialog -->
  <el-dialog
    v-model="subjectDialogVisible"
    title="科目列表"
    width="30%"
  >
    <el-table :data="subjectList" style="width: 100%">
      <el-table-column prop="subjectName" label="科目名称" />
    </el-table>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { Edit, Plus, QuestionFilled, CircleCheck, Ticket, UserFilled } from '@element-plus/icons-vue';
import MemberUpdate from './memberUpdate.vue';
import ScheduleShow from '@/views/system/studyPlan/scheduleShow.vue';
import MistakeNote from '@/views/printApply/mistakeNote.vue';
import StudyRecord from '@/views/study/studyRecord/index.vue';
const activeTab = ref('profile');
const graduationAvatar = new URL('@/assets/images/graduation-avatar.svg', import.meta.url).href;
import { getMember } from '@/api/system/member';
import { MemberVO } from '@/api/system/member/types';
import { selectSubjectByUserId } from '@/api/courseSubject';

const userinfo = ref<Partial<MemberVO>>({});

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const { sys_member_grade,sys_member_source } = toRefs<any>(proxy?.useDict('sys_member_grade','sys_member_source'));

const route = useRoute();

const tabParam = ref({});

const userId = ref<string|number>('');

const subjectDialogVisible = ref(false);
const subjectList = ref([]);

/** 修改按钮操作  */
const getMemberInner = async (userId?: string | number) => {
  const res = await getMember(userId);
  userinfo.value = res.data;
  //tabParam.value.userId = userId;
  tabParam.value = { ...res.data };
}
const refashMember = () => {
  getMemberInner(userId.value);
}

const showSubjects = async () => {
  try {
    const res = await selectSubjectByUserId(userinfo.value.userId);
    subjectList.value = res.data;
    subjectDialogVisible.value = true;
  } catch (error) {
    console.error('Failed to fetch subjects:', error);
  }
};

onMounted(() => {
  userId.value = route.params && (route.params.userId as string | number);
  getMemberInner(userId.value);
});
</script>

<style scoped>
.member-detail {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header-cards {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.member-info-card, .subject-info-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.member-info {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.avatar-info {
  display: flex;
  gap: 24px;
}

.profile-avatar {
  background-color: #ff7875;
  font-size: 40px;
}

.basic-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.name-row {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.name-section {
  display: flex;
  align-items: center;
  gap: 12px;
  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #1f2329;
  }
}

.member-tag {
  border-radius: 4px;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
  font-size: 12px;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 4px;
  .el-icon {
    font-size: 12px;
  }
}

.info-row {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #4e5969;
  font-size: 14px;
  .el-divider {
    margin: 0;
    border-color: #e4e7ed;
  }
}

.tutor-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  .label {
    color: #4e5969;
  }
  .verified {
    color: #67c23a;
    margin-left: 4px;
  }
  .pending {
    color: #ff4d4f;
  }
}

.action-buttons {
  display: flex;
  gap: 12px;
  :deep(.el-button) {
    padding: 9px 20px;
    font-size: 14px;
    border-radius: 4px;
  }
}

.subject-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 10px;
}

.top-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.member-card {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  padding: 0;
  height: auto;
  .el-icon {
    font-size: 16px;
  }
}

.view-all {
  font-size: 14px;
}

.subject-scope {
  color: #4e5969;
  font-size: 14px;
  .separator {
    margin: 0 8px;
    color: #c9cdd4;
  }
}

.points-section {
  display: flex;
  align-items: center;
  gap: 4px;
  .label {
    color: #4e5969;
    font-size: 14px;
  }
  .el-icon {
    color: #909399;
    font-size: 14px;
  }
  .points {
    font-size: 24px;
    font-weight: 600;
    color: #1f2329;
    margin-left: 4px;
  }
}

.nav-tabs {
  margin-bottom: 16px;
  :deep(.el-tabs__header) {
    margin-bottom: 16px;
    border-radius: 8px;
    background-color: #fff;
  }
  :deep(.el-tabs__nav-wrap) {
    padding: 0 24px;
  }
  :deep(.el-tabs__item) {
    font-size: 14px;
    height: 48px;
    line-height: 48px;
    color: #4e5969;
    &.is-active {
      color: var(--el-color-primary);
      font-weight: 500;
    }
  }
  :deep(.el-tabs__active-bar) {
    height: 3px;
    border-radius: 1.5px;
  }
}

.profile-content {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  
  .profile-header {
    margin-bottom: 24px;
    box-shadow: none;
    
    .title-section {
      display: flex;
      align-items: center;
      gap: 8px;
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #1f2329;
      }
      
      .edit-icon {
        font-size: 14px;
        color: var(--el-color-primary);
      }
    }
  }

  :deep(.el-descriptions) {
    --el-descriptions-item-bordered-label-background: #fafafa;
    
    .el-descriptions__cell {
      padding: 12px 16px;
    }
    
    .el-descriptions__label {
      color: #4e5969;
      font-weight: normal;
    }
    
    .el-descriptions__content {
      color: #1f2329;
    }
  }
}

.tags-section {
  margin-top: 24px;
  
  .tags-header {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .label {
      font-size: 14px;
      color: #1f2329;
      font-weight: 600;
    }
    
    .el-button {
      padding: 0;
      height: auto;
    }
  }
}
</style>