<template>
  <div class="p-2">
     <!-- 添加或修改用户信息对话框 -->
     <el-dialog :title="dialog.title" v-model="dialog.visible" width="700px" append-to-body>
      <el-form ref="memberFormRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="设置班型：" prop="typeIds">
          <el-checkbox-group v-model="form.typeIds">
              <el-checkbox 
                v-for="option in classTypeList" 
                :key="option.typeId" 
                :value="option.typeId"
              >
                {{ option.typeName }}
              </el-checkbox>
            </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ClassTypeSet" lang="ts">
import { MemberClassTypeForm } from '@/api/system/member/types';
import {optionSelectClassType} from '@/api/course/classType';
import { setClassType } from '@/api/system/member';
import {queryListUserClassType} from '@/api/system/userClassType'
import {UserClassTypeQuery} from '@/api/system/userClassType/types'

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_member_source, sys_member_grade } = toRefs<any>(proxy?.useDict('sys_member_source', 'sys_member_grade'));
const route = useRoute();

const buttonLoading = ref(false);

const memberFormRef = ref<ElFormInstance>();

const  queryParams = ref<UserClassTypeQuery>({
    userId: undefined,
    pageNum: 1,
    pageSize: 10,

});

const form = reactive<MemberClassTypeForm>({
  userId: undefined,
  typeIds: undefined
});
const rules = ref({ 
  typeIds: [
    { required: true, message: "请选择班型", trigger: "blur" }
  ]
});

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

/** 提交按钮 */
const submitForm = () => {
  memberFormRef.value?.validate(async (valid: boolean) => {
  if (valid) {
   await setClassType(form);
   proxy?.$modal.msgSuccess("操作成功");
   proxy.$emit('close_class_type', {});
   dialog.visible = false;
  }
});
};

const classTypeList = ref([]); //班型选择
const initOptionClassType = async () => {
    const classTypeRes = await optionSelectClassType();
    classTypeList.value = classTypeRes.data;
};

const props = defineProps({
  param: { type: null, required: true }
});

const formParam = computed(() => props.param);
/** 取消按钮 */
const cancel = () => {
  proxy.$emit('close_class_type', {});
  dialog.visible = false;
}
const queryListUserClassTypeInner= async () =>{
    const res = await queryListUserClassType(queryParams.value);
    form.typeIds = res.data?.map(item => item.typeId) || [];
}
//closeClassType
onMounted(() => {
  form.userId = formParam.value.userId;
  initOptionClassType();
  queryParams.value.userId = formParam.value.userId;
  dialog.visible = true;
  queryListUserClassTypeInner();
});
</script>
