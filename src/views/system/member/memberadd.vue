<template>
  <div class="p-2">
    <el-form ref="memberFormRef" :model="form" :rules="rules" label-width="90px">
      <el-row style="margin: 0;">
        <el-col :span="1" ><div class="grid-content ep-bg-purple" ></div></el-col>
        <el-col :span="10"  style="margin-top: 10px;"><div class="grid-content ep-bg-purple-dark bold-text" >账号信息</div></el-col>
      </el-row>
      <el-row style="margin: 0;">
        <el-col :span="1" ><div class="grid-content ep-bg-purple" ></div></el-col>
        <el-col :span="23" ><div class="grid-content ep-bg-purple-dark"><el-divider style="margin: 5px 0;"></el-divider></div></el-col>
      </el-row>
    
      <el-row style="margin: 0;">
        <el-col :span="2"><div class="grid-content ep-bg-purple" ></div></el-col>
        <el-col :span="12">
            <div class="grid-content ep-bg-purple-dark" >
                <el-form-item label="用户账号" prop="userName" label-width="90px">
                  <el-input v-model="form.userName" placeholder="请输入正确的手机号码" />
                </el-form-item>
                <el-form-item v-if="form.userId == undefined" label="用户密码" prop="password">
                  <el-input v-model="form.password" placeholder="初始密码默认账号后6位" type="password" maxlength="20" show-password />
                </el-form-item>
                <el-form-item label="姓名" prop="nickName" label-width="90px">
                  <el-input v-model="form.nickName" placeholder="请输入姓名" />
                </el-form-item>
                <el-form-item label="用户性别" prop="sex" label-width="90px">
                    <el-select v-model="form.sex" placeholder="请选择">
                      <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="年级" prop="grade" label-width="90px">
                    <el-select v-model="form.grade" placeholder="请选择">
                      <el-option v-for="dict in sys_member_grade" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="来源" prop="source" label-width="90px">
                    <el-select v-model="form.source" placeholder="请选择">
                      <el-option v-for="dict in sys_member_source" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="伴学师" prop="studyMentorId" label-width="90">
                  <el-select v-model="form.studyMentorId" placeholder="请选择伴学师" clearable>
                    <el-option v-for="mentor in mentorList" :key="mentor.userId" :label="mentor.nickName"
                              :value="mentor.userId"/>
                  </el-select>
                </el-form-item>
                  
                  <el-form-item label="家长电话1" prop="parentPhoneFirst" label-width="90px">
                    <el-input v-model="form.parentPhoneFirst" placeholder="请输入家长电话1" />
                  </el-form-item>
                  <el-form-item label="家长电话2" prop="parentPhoneSecond" label-width="90px">
                    <el-input v-model="form.parentPhoneSecond" placeholder="请输入家长电话2" />
                  </el-form-item>
            </div>
        </el-col>
      </el-row>
      
      <el-row style="margin: 0;">
        <div class="decorated-line">
          <span class="text">以上信息为 <span class="highlight">添加账号</span> 必填；请尽可能完善以下信息以更好的提供服务</span>
        </div>
      </el-row>

      <el-row>
        <el-col :span="2"><div class="grid-content ep-bg-purple" ></div></el-col>
        <el-col :span="4">
            <div class="grid-content ep-bg-purple-dark" >
                <el-form-item label="省" prop="provinceCode" label-width="90">
                  <el-select v-model="form.provinceCode" placeholder="请选择省" clearable  @change="handleProvinceChange">
                    <el-option v-for="province in provinceList" :key="province.code" :label="province.name"
                              :value="province.code"/>
                  </el-select>
                </el-form-item>
            </div>
        </el-col>
        <el-col :span="4">
            <div class="grid-content ep-bg-purple-dark" >
              <el-form-item label="市" prop="cityCode" label-width="50">
                  <el-select v-model="form.cityCode" placeholder="请选择市" clearable
                  @change="handleCityChange">
                    <el-option v-for="city in cityList" :key="city.code" :label="city.name"
                              :value="city.code"/>
                  </el-select>
                </el-form-item>
            </div>
        </el-col>
        <el-col :span="4">
            <div class="grid-content ep-bg-purple-dark" >
              <el-form-item label="区（县）" prop="districtCode" label-width="90">
                  <el-select v-model="form.districtCode" placeholder="请选择区" clearable @change="handleDistrictChange">
                    <el-option v-for="district in districtList" :key="district.code" :label="district.name"
                              :value="district.code"/>
                  </el-select>
                </el-form-item>
            </div>
        </el-col>
        <el-col :span="4">
            <div class="grid-content ep-bg-purple-dark" >
                <el-form-item label="在读学校" prop="currSchool" label-width="90">
                  <el-select v-model="form.currSchool" placeholder="请选择学校" clearable>
                    <el-option v-for="school in schoolList" :key="school.id" :label="school.schoolName"
                              :value="school.id"/>
                  </el-select>
                </el-form-item>
            </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="2"><div class="grid-content ep-bg-purple" ></div></el-col>
        <el-col :span="6">
            <div class="grid-content ep-bg-purple-dark" >
                <el-form-item label="在校班级" prop="classSchool" label-width="90px">
                  <el-input v-model="form.classSchool" placeholder="请输入在校班级" />
                </el-form-item>
            </div>
        </el-col>
        <el-col :span="6">
            <div class="grid-content ep-bg-purple-dark" >
                <el-form-item label="公立学校学号" prop="studentNum" label-width="120px">
                  <el-input v-model="form.studentNum" placeholder="请输入公立学校学号" />
                </el-form-item>
            </div>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="2"><div class="grid-content ep-bg-purple" ></div></el-col>
        <el-col :span="6">
            <div class="grid-content ep-bg-purple-dark" >
                <el-form-item label="文理科" prop="sciences" label-width="90px">
                    <el-select v-model="form.sciences" placeholder="请选择">
                      <el-option v-for="dict in sys_member_sciences" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                    </el-select>
                </el-form-item>
            </div>
        </el-col>
        <el-col :span="6">
            <div class="grid-content ep-bg-purple-dark" >
                <el-form-item label="住校情况" prop="boardingStatus" label-width="90px">
                    <el-select v-model="form.boardingStatus" placeholder="请选择">
                      <el-option v-for="dict in sys_member_board" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                    </el-select>
                </el-form-item>
            </div>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="2"><div class="grid-content ep-bg-purple" ></div></el-col>
        <el-col :span="12">
            <div class="grid-content ep-bg-purple-dark" >
                <el-form-item label="家庭住址" prop="homeAddress" label-width="90px">
                  <el-input v-model="form.homeAddress" placeholder="请输入家庭住址" />
                </el-form-item>
            </div>
        </el-col>
      </el-row>


      <el-row>
        <el-col :span="1" ><div class="grid-content ep-bg-purple" ></div></el-col>
        <el-col :span="10" ><div class="grid-content ep-bg-purple-dark bold-text" ></div></el-col>
      </el-row>
      <el-row class="row-bg footer-buttons">
        <el-col :span="11"><div class="grid-content ep-bg-purple" ></div>
        </el-col>
        <el-col :span="3"><div class="grid-content ep-bg-purple-light button-container" >
            <el-button :loading="buttonLoading" type="primary" @click="submitForm">添加账号</el-button>
            <el-button @click="handleClose">   关闭   </el-button>
          </div>
        </el-col>
        <el-col :span="9"><div class="grid-content ep-bg-purple" ></div>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup name="Memberadd" lang="ts">
import { listMember, getMember, delMember, addMember, updateMember } from '@/api/system/member';
import {optionSelectByDeptId} from '@/api/system/user';
import {getMyDept} from '@/api/system/dept';
import { MemberVO, MemberQuery, MemberForm } from '@/api/system/member/types';
import { RouteLocationNormalized } from 'vue-router';
import { listAreaF } from '@/api/system/area';
import { listAreaSchoolF } from '@/api/system/areaschool';

const route = useRoute();

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const {  sys_user_sex,sys_member_grade,sys_member_source,sys_member_sciences,sys_member_board } = toRefs<any>(proxy?.useDict('sys_user_sex','sys_member_grade',"sys_member_source","sys_member_sciences","sys_member_board"));

const memberFormRef = ref<ElFormInstance>();

const buttonLoading = ref(false);

const initFormData: MemberlistForm = {
  deptId: undefined,
  userName: undefined,
  password: undefined,
  sex: undefined,
  avatar: undefined,
  status: undefined,
  loginIp: undefined,
  loginDate: undefined,
  remark: undefined,
  grade: undefined,
  source: undefined,
  parentPhoneFirst: undefined,
  parentPhoneSecond: undefined,
  studyMentor: undefined,
  homeAddress: undefined,
  provinceCode: undefined,
  cityCode: undefined,
  districtCode: undefined,
  nickName: undefined,


  /**
   * 在读学校
   */
   currSchool: undefined,

  /**
   * 班级
   */
  classSchool: undefined,

  /**
   * 公立学校学号
   */
  studentNo: undefined,

  /**
   * 文理科
   */
  sciences: undefined,

  /**
   * 住校情况
   */
  boardingStatus: undefined

}
// 手机号校验规则
const validateAccountPhone = (rule, value, callback) => {
    if (!value) {
      return callback(new Error('请输入手机号码'));
    }
    const reg = /^(13[0-9]|14[0-9]|15[0-9]|16[6]|17[0-9]|18[0-9]|19[8-9])\d{8}$/;
    if (!reg.test(value)) {
      callback(new Error('请输入有效的手机号码'));
    } else {
      form.value.password = form.value.userName.slice(-6);
      callback();
    }
};
//  手机号校验规则
const validatePhone = (rule, value, callback) => {
    if (!value) {
      callback();
      return;
    }
    const reg = /^(13[0-9]|14[0-9]|15[0-9]|16[6]|17[0-9]|18[0-9]|19[8-9])\d{8}$/;
    if (!reg.test(value)) {
      callback(new Error('请输入有效的手机号码'));
    } else {
      callback();
    }
};
const data = reactive({
  form: {...initFormData},
  rules: {
    userName: [
      { required: true, message: "用户账号不能为空", trigger: "blur" },
      { validator: validateAccountPhone, trigger: 'blur' }
    ],
    nickName: [
      { required: true, message: "用户姓名不能为空", trigger: "blur" }
    ],
    sex: [
      { required: true, message: "用户性别不能为空", trigger: "blur" }
    ],
    grade: [
      { required: true, message: "年级不能为空", trigger: "blur" }
    ],
    source: [
      { required: true, message: "来源不能为空", trigger: "blur" }
    ],
    parentPhoneFirst: [
      { required: true, message: "家长电话不能为空", trigger: "blur" },
      { validator: validatePhone, trigger: 'blur' }
    ],
    parentPhoneSecond: [
      { validator: validatePhone, trigger: 'blur' }
    ],
    studyMentorId: [
    { required: true, message: "伴学师不能为空", trigger: "blur" }
    ],
    password: [
      { required: true, message: "密码不能为空", trigger: "blur" }
    ]
  }
});

const { form, rules } = toRefs(data);

const handleClose = () => {
  const obj: RouteLocationNormalized = {
    fullPath: '',
    hash: '',
    matched: [],
    meta: undefined,
    name: undefined,
    params: undefined,
    redirectedFrom: undefined,
    path: '/system/member/member',
    query: { t: Date.now().toString(),isRefresh:true}
  };
  proxy?.$tab.closeOpenPage(obj);
};
/** 提交按钮 */
const submitForm = () => {
  memberFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      form.value.deptId=route.query.deptId;
      await addMember(form.value).finally(() =>  buttonLoading.value = false);
      proxy?.$modal.msgSuccess("操作成功");
      handleClose();
    }
  });
}
 /** 省下拉列表 begin*/
const provinceList = ref([]); //
const getProvinceList = async () => {
    //查询省数据
    const query = {
      level: 1
    };
    const res = await listAreaF(query);
    provinceList.value = res.rows;
  };
  /** 市下拉列表 begin*/
  const cityList = ref([]); //
  const handleProvinceChange = async (value: number | string) => {
      cityList.value = [];
      //如果有变化,清空下拉列表中key、value
      form.value.cityCode = '';

      districtList.value = [];
      form.value.districtCode = '';

      schoolList.value = [];
      form.value.currSchool = '';
      if (value != undefined && value != '') {
        //查询省数据
        const query = {
          parentCode: value
        };
        const res = await listAreaF(query);
        cityList.value = res.rows;
      }
  };
 /** 市下拉列表 begin*/
const districtList = ref([]); //
const handleCityChange = async (value: number | string) => {
  if (value != undefined && value != '') {
    //查询省数据
    const query = {
      parentCode: value
    };
    const res = await listAreaF(query);
    districtList.value = res.rows;
  } else {
    //清除市下拉
    districtList.value = [];
  }
};

const schoolList = ref([]); //
const handleDistrictChange = async (value: number | string) => {
  if (value != undefined && value != '') {
    //查询省数据
    const query = {
      areaCode: value
    };
    const res = await listAreaSchoolF(query);
    schoolList.value = res.rows;
  } else {
    //清除市下拉
    schoolList.value = [];
  }
};

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  memberFormRef.value?.resetFields();
}
const initPassword = ref<string>('');

const mentorList = ref([]); //
const initDeptUserOption = async () => {
    const res = await getMyDept();
    const users = await optionSelectByDeptId(res.data.deptId);
    mentorList.value = users.data;
};

onMounted(() => {
  getProvinceList();
  proxy?.getConfigKey('sys.user.initPassword').then((response) => {
    initPassword.value = response.data;
  });
  initDeptUserOption();
});
</script>


<style lang="scss">
.el-row {
  margin-bottom: 20px;
}
.el-row:last-child {
  margin-bottom: 0;
}
.el-col {
  border-radius: 4px;
}

.grid-content {
  border-radius: 4px;
  min-height: 36px;
}
.bold-text {
  font-weight: bold; /* 加粗 */
}

.decorated-line {
  display: flex;
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  width: 100%; /* 容器宽度 */
  margin: 20px 0; /* 外边距 */
}

.decorated-line .text {
  position: relative;
  padding: 0 20px; /* 文本两侧留出空间 */
  white-space: nowrap; /* 禁止换行 */
  font-size: 16px; /* 字体大小 */
  color: #409EFF; /* 蓝色文字 */
  z-index: 1; /* 确保文本在装饰线上方 */
}

/* 左边的直线 */
.decorated-line .text::before {
  content: '';
  position: absolute;
  left: -50%;
  top: 50%;
  transform: translateY(-50%);
  width: 50%; /* 直线长度 */
  height: 1px; /* 直线高度 */
  background-color: #E4E7ED; /* 直线颜色 */
  z-index: -1; /* 确保装饰线在文本下方 */
}

/* 右边的直线 */
.decorated-line .text::after {
  content: '';
  position: absolute;
  right: -50%;
  top: 50%;
  transform: translateY(-50%);
  width: 50%; /* 直线长度 */
  height: 1px; /* 直线高度 */
  background-color: #E4E7ED; /* 直线颜色 */
  z-index: -1; /* 确保装饰线在文本下方 */
}

/* 高亮部分 */
.highlight {
  color: #F56C6C; /* 红色文字 */
}

.footer-buttons {
  position: fixed; /* 固定定位 */
  bottom: 0; /* 距离底部为 0 */
  left: 0;
  right: 0;
  background-color: #fff; /* 背景颜色，避免遮挡内容 */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
  padding: 10px 20px; /* 内边距 */
  z-index: 1000; /* 确保按钮层在最上层 */
}

.button-container {
  display: flex;
  justify-content: space-between; /* 按钮左右分布 */
  align-items: center; /* 垂直居中对齐 */
  padding: 3px 0;
  margin: 3px 0;
}
/* 调整按钮大小 */
.el-button--small {
  padding: 5px 10px; /* 减小按钮的内边距 */
  font-size: 12px; /* 减小字体大小 */
}
</style>