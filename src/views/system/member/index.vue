<template>
  <div class="p-2">
   <el-row :gutter="20">
      <!-- 部门树 -->
      <el-col :lg="5" :xs="24" style="">
        <el-card shadow="hover" v-loading="loading">
          <el-input v-model="deptName" placeholder="请输入部门名称" prefix-icon="Search" clearable />
          <el-tree
            ref="deptTreeRef"
            class="mt-2"
            node-key="id"
            :data="deptOptions"
            :props="{ label: 'label', children: 'children' }"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            highlight-current
            default-expand-all
            @node-click="handleNodeClick"
          />
        </el-card>
      </el-col>
      <el-col :lg="19" :xs="24">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
          <div v-show="showSearch" class="mb-[10px]">
            <el-card shadow="hover">
              <el-form ref="queryFormRef" :model="queryParams" :inline="true">
              <el-form-item label="用户账号" prop="userName">
                <el-input v-model="queryParams.userName" placeholder="请输入用户账号" clearable @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item label="年级" prop="grade">
                <el-select v-model="queryParams.grade" placeholder="请选择年级" clearable >
                <el-option v-for="dict in sys_member_grade" :key="dict.value" :label="dict.label" :value="dict.value"/>
                </el-select>
              </el-form-item>
              <el-form-item label="学生来源" prop="source">
                <el-select v-model="queryParams.source" placeholder="请选择学生来源" clearable >
                <el-option v-for="dict in sys_member_source" :key="dict.value" :label="dict.label" :value="dict.value"/>
                </el-select>
              </el-form-item>
              <el-form-item label="家长电话" prop="parentPhoneFirst">
                <el-input v-model="queryParams.parentPhoneFirst" placeholder="请输入家长电话" clearable @keyup.enter="handleQuery" />
              </el-form-item>
               <el-form-item label="伴学师" prop="studyMentorId" label-width="90">
                  <el-select v-model="queryParams.studyMentorId" placeholder="请选择伴学师" clearable>
                    <el-option v-for="mentor in mentorList" :key="mentor.userId" :label="mentor.nickName"
                              :value="mentor.userId"/>
                  </el-select>
                </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
              </el-form>
            </el-card>
          </div>
		    </transition>
        <el-card shadow="never">
          <template #header>
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
              <el-button :disabled="addButtonDisabled" type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:member:add']">新增</el-button>
              </el-col>
              <el-col :span="1.5">
              <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['system:member:edit']">修改</el-button>
              </el-col>
              <el-col :span="1.5">
              <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['system:member:remove']">删除</el-button>
              </el-col>
              <el-col :span="1.5">
              <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:member:export']">导出</el-button>
              </el-col>
              <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>
          </template>
      
          <el-table v-loading="loading" :data="memberList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="用户ID" align="center" prop="userId" width="180"/>
            <el-table-column label="用户账号" align="center" prop="userName" width="150"/>
            <el-table-column label="菁优开通状态" align="center" prop="jyeooCode" width="150">
              <template #default="scope">
                <el-tag :type="scope.row.jyeooCode ? 'success' : 'info'">
                  {{ scope.row.jyeooCode ? '开通' : '未开通' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="菁优账号" align="center" prop="jyeooCode" width="150" :show-overflow-tooltip="true"/>
            <el-table-column label="用户姓名" align="center" prop="nickName"  :show-overflow-tooltip="true">
              <template #default="scope">
              <router-link :to="'/system/member-memberdetail/index/' + scope.row.userId" class="link-type">
                <span>{{ scope.row.nickName}}</span>
              </router-link>
              </template>
            </el-table-column>
            <el-table-column label="伴学师" align="center" prop="studyMentorNickName" />
            <el-table-column label="状态" align="center" prop="status" >
                <template #default="scope">
                <el-switch v-model="scope.row.status" active-value="0" inactive-value="1" @change="handleStatusChange(scope.row)"></el-switch>
                </template>
              </el-table-column>
            <el-table-column label="起始时间" align="center" prop="startTime" width="180">
              <template #default="scope">
              <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="结束时间" align="center" prop="endTime" width="180">
              <template #default="scope">
              <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="年级" align="center" prop="grade">
              <template #default="scope">
              <dict-tag :options="sys_member_grade" :value="scope.row.grade"/>
              </template>
            </el-table-column>
            <el-table-column label="学生来源" align="center" prop="source" width="120">
              <template #default="scope">
              <dict-tag :options="sys_member_source" :value="scope.row.source"/>
              </template>
            </el-table-column>
            <el-table-column label="家长电话" align="center" prop="parentPhoneFirst"  width="150"/>
            <el-table-column label="操作" fixed="right" align="center" class-name="small-padding fixed-width"  width="150">
              <template #default="scope">
              <el-tooltip content="授权账号" placement="top">
                <el-button link type="primary" icon="Key" @click="handleUpdate(scope.row)" v-hasPermi="['system:member:auth']"></el-button>
              </el-tooltip>
              <el-tooltip content="设置班型" placement="top">
                <el-button link type="primary" icon="School" @click="handleClassType(scope.row)" v-hasPermi="['system:member:classtype']"></el-button>
              </el-tooltip>
               <el-tooltip v-if="!scope.row.jyeooCode" content="菁优开户" placement="top">
                <el-button link type="primary" icon="User" :loading="registerLoading" @click="handRegisterJeyoo(scope.row)" v-hasPermi="['system:member:jeyoo']"></el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:member:remove']"></el-button>
              </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
        </el-card>
      </el-col>
    </el-row>
    <!-- 添加或修改用户信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="700px" append-to-body>
      <el-form ref="memberFormRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="我的剩余积分：">
          <span>{{form.availableCount}}</span>
        </el-form-item>
        <el-form-item label="分配账号：">
          <span>{{form.userName}}</span>
        </el-form-item>
        <el-form-item label="账号姓名：">
          <span>{{form.nickName}}</span>
        </el-form-item>
        <el-form-item label="会员类型：" prop="memberType">
          <el-radio-group v-model="form.memberType" @change="memberTypeChange">
            <el-radio-button
              v-for="option in pointsOptions" 
              :key="option.configId" 
              :value="option.configId"
            >
              {{ option.configName }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="会员到期时间：">
          <span v-if="form.endTime">{{form.endTime}}</span>
        </el-form-item>

        <el-row style="margin: 0;">
            <el-col :span="10">
              <div class="grid-content ep-bg-purple" >
                <el-form-item label="会员起算日期：" prop="startTimeNew">
                  <el-date-picker clearable
                    v-model="form.startTimeNew"
                    type="date"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择会员起算日期："
                    @change="startTimeNewChange">
                </el-date-picker>
                </el-form-item>
              </div>
            </el-col>
            <el-col :span="10">
               <div class="grid-content ep-bg-purple" >
                <el-form-item label="至：" prop="endTimeNew">
                  <el-date-picker clearable
                    v-model="form.endTimeNew"
                    type="date"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择会员结束日期：">
                </el-date-picker>
                </el-form-item>
              </div>
            </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <ClassTypeSet v-if="classTypeDilogShow" :param="classTypeParam" @close_class_type="closeClassType"></ClassTypeSet>
  </div>
</template>

<script setup name="Member" lang="ts">
import api from '@/api/system/user';
import {optionSelectByDeptId} from '@/api/system/user';
import { UserVO } from '@/api/system/user/types';
import { DeptVO } from '@/api/system/dept/types';
import {  getDept,getMyDept } from '@/api/system/dept';
import { MemberVO, MemberQuery, MemberForm } from '@/api/system/member/types';
import { authMember,listMember, getMember, delMember, addMember, updateMember,changeMemberStatus,registerJeyoo } from '@/api/system/member';
import { pointsQueryList } from '@/api/pointsConfig';
import { MemberPointsConfigVO, MemberPointsConfigQuery } from '@/api/pointsConfig/types';
import ClassTypeSet from './classTypeSet.vue';
import {formatDateToStr} from '@/utils';


const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_member_source, sys_member_grade } = toRefs<any>(proxy?.useDict('sys_member_source', 'sys_member_grade'));
const route = useRoute();

const memberList = ref<MemberVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const registerLoading = ref(false);

const queryFormRef = ref<ElFormInstance>();
const memberFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const validateTime = (rule, value, callback) => {
  if(!form.value.startTimeNew || !form.value.endTimeNew){
    callback();
  }
  if (form.value.startTimeNew >= form.value.endTimeNew) {
    callback(new Error('开始日期不能大于结束日期'));
  } else {
    callback();
  }
};
const initFormData: MemberForm = {
  userId: undefined,
  deptId: undefined,
  userName: undefined,
  nickName: undefined,
  userType: undefined,
  email: undefined,
  phonenumber: undefined,
  sex: undefined,
  avatar: undefined,
  password: undefined,
  status: undefined,
  loginIp: undefined,
  loginDate: undefined,
  remark: undefined,
  memberType: undefined,
  startTime: undefined,
  endTime: undefined,
  grade: undefined,
  source: undefined,
  parentPhoneFirst: undefined,
  parentPhoneSecond: undefined,
  studyMentorId: undefined,
  studyMentorName: undefined,
  homeAddress: undefined,
  provinceCode: undefined,
  cityCode: undefined,
  districtCode: undefined,
  classSchool: undefined,
  studentNum: undefined,
  sciences: undefined,
  boardingStatus: undefined,
  parentOpenid: undefined,
  currSchool: undefined,
  availableCount: undefined,
  startTimeNew: undefined,
  endTimeNew: undefined,
  quarterAvailableCount: undefined
}
const data = reactive<PageData<MemberForm, MemberQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: undefined,
    phonenumber: undefined,
    grade: undefined,
    source: undefined,
    parentPhoneFirst: undefined,
    studyMentorName: undefined,
    deptId: undefined,
    params: {
    }
  },
  rules: {
    startTimeNew: [
      { required: true, message: "开始时间不能为空", trigger: "blur" }
    ],
    memberType: [
      { required: true, message: "会员类型不能为空", trigger: "blur" }
    ],
    endTimeNew: [
      { required: true, message: "结束时间不能为空", trigger: "blur" },
      { validator: validateTime, trigger: 'blur' }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询用户信息列表 */
const getList = async () => {
  loading.value = true;
  if(queryParams.value && queryParams.value.deptId){
    const res = await listMember(queryParams.value);
    memberList.value = res.rows;
    total.value = res.total;
  }
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  memberFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: MemberVO[]) => {
  ids.value = selection.map(item => item.userId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  if(!queryParams.value.deptId){
    return;
  }
  proxy.$router.push({
    path: '/system/member-memberadd/index', // 对应路由配置的path
    query: { action: 'add',deptId:queryParams.value.deptId } // 标识新增操作
  });
}

/** 修改按钮操作 */
const handleUpdate = async (row?: MemberVO) => {
  reset();
  const _userId = row?.userId || ids.value[0]
  const res = await getMember(_userId);
  const resMyDept = await getMyDept();
  Object.assign(form.value, res.data);
  form.value.authType='year';
  form.value.availableCount = resMyDept.data.availableCount;
  dialog.visible = true;
  dialog.title = "授权账号";
}

const classTypeParam = ref({});
const classTypeDilogShow = ref(false);
const handleClassType = (row?: MemberVO) => {
  reset();
  const _userId = row?.userId || ids.value[0];
  classTypeParam.value.userId = _userId;
  classTypeDilogShow.value = true;
}

const closeClassType = () =>{
  classTypeDilogShow.value = false;
}

/** 菁优开户操作 */
const handRegisterJeyoo = async (row?: MemberVO) => {
  const _userId = row?.userId || ids.value[0];
  registerLoading.value = true;
  try {
    await proxy?.$modal.confirm('是否确认为该用户开通菁优账号？');
    const form: MemberForm = {
      userId: _userId,
      availableCount: 0,
      startTimeNew: undefined,
      endTimeNew: undefined
    };
    await registerJeyoo(form);
    proxy?.$modal.msgSuccess("开户成功");
    await getList();
  } catch (error) {
    console.error('开户失败:', error);
  } finally {
    registerLoading.value = false;
  }
}

/** 删除按钮操作 */
const handleDelete = async (row?: MemberVO) => {
  const _userIds = row?.userId || ids.value;
  await proxy?.$modal.confirm('是否确认删除用户信息编号为"' + _userIds + '"的数据项？').finally(() => loading.value = false);
  await delMember(_userIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('system/member/export', {
    ...queryParams.value
  }, `member_${new Date().getTime()}.xlsx`)
}

/** 用户状态修改  */
const handleStatusChange = async (row: UserVO) => {
  let text = row.status === '0' ? '启用' : '停用';
  try {
    await proxy?.$modal.confirm('确认要"' + text + '""' + row.userName + '"会员吗?');
    await changeMemberStatus(row.userId, row.status);
    proxy?.$modal.msgSuccess(text + '成功');
  } catch (err) {
    row.status = row.status === '0' ? '1' : '0';
  }
};

const deptName = ref('');
const deptTreeRef = ref<ElTreeInstance>();
const deptOptions = ref<DeptVO[]>([]);
    /** 根据名称筛选部门树 */
    watchEffect(
  () => {
    deptTreeRef.value?.filter(deptName.value);
  },
  {
    flush: 'post' // watchEffect会在DOM挂载或者更新之前就会触发，此属性控制在DOM元素更新后运行
  }
);

/** 通过条件过滤节点  */
const filterNode = (value: string, data: any) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};


/** 查询部门下拉树结构 */
const getTreeSelect = async () => {
  const res = await api.deptTreeSelect();
  deptOptions.value = res.data;
  if(deptOptions && deptOptions.value.length==1){
     queryParams.value.deptId = deptOptions.value[0].id;
     getList();
     const resDept = await getDept(queryParams.value.deptId);
    if(resDept.data.areaLevel && resDept.data.areaLevel==='school'){
      addButtonDisabled.value = false;
    }else{
      addButtonDisabled.value = true;
    }
  }
};
const addButtonDisabled = ref(true);
/** 节点单击事件 */
const handleNodeClick = async (data: DeptVO) => {
  loading.value = true;
  queryParams.value.deptId = data.id;
  const resDept = await getDept(data.id);
  if(resDept.data.areaLevel && resDept.data.areaLevel==='school'){
    addButtonDisabled.value = false;
  }else{
    addButtonDisabled.value = true;
  }
  handleQuery();
};
/**
 * 计算会员结束日期
 * 
*/
const setEndTimeNew = () => {
  const memberType = form.value.memberType;
  const targets = pointsOptions.value.filter(item => item.configId === memberType);
  form.value.endTimeNew = null;
  if(targets[0].configType==='year'){
    const dateTemp = new Date(form.value.startTimeNew);
    dateTemp.setFullYear(dateTemp.getFullYear() + targets[0].configTerm);
    form.value.endTimeNew = formatDateToStr(dateTemp);
  }
  if(targets[0].configType==='month'){
    const dateTemp = new Date(form.value.startTimeNew);
    dateTemp.setMonth(dateTemp.getMonth() + targets[0].configTerm);
    form.value.endTimeNew = formatDateToStr(dateTemp);
  }
  if(targets[0].configType==='day'){
    const dateTemp = new Date(form.value.startTimeNew);
    dateTemp.setDate(dateTemp.getDate() + targets[0].configTerm);
    form.value.endTimeNew = formatDateToStr(dateTemp);
  }
}
// 会员类型变化
const memberTypeChange = (newVal) => {
  if(!form.value.endTime){
    form.value.startTimeNew = formatDateToStr(new Date());
  }else{
    const date = new Date(form.value.endTime);
      // 加一天
    date.setDate(date.getDate() + 1);
    form.value.startTimeNew = formatDateToStr(date);
  }
  setEndTimeNew();
};
// 处理日期改变的函数
const startTimeNewChange = (newDate) => {
  setEndTimeNew();
};

/** 提交按钮 */
const submitForm = () => {
    if(buttonLoading.value){
      return;
    }
    buttonLoading.value=true;
    memberFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      try {
        await authMember(form.value);
        proxy?.$modal.msgSuccess('操作成功');
        dialog.visible = false;
        getList();
      } catch (error) {
        console.error('认证失败:', error);
      } finally {
        buttonLoading.value=false;
      }
    }
    buttonLoading.value=false;
  });
};
const pointsOptions = ref<MemberPointsConfigVO[]>([]);
const pointsQueryListOptions = async()=>{
  const query: MemberPointsConfigQuery = {
      pageNum: 1,
      pageSize: 10
    };
  const res = await pointsQueryList(query);
  pointsOptions.value=res.data;
};
const mentorList = ref([]); //
const initDeptUserOption = async () => {
    const res = await getMyDept();
    const users = await optionSelectByDeptId(res.data.deptId);
    mentorList.value = users.data;
};
onMounted(() => {
  getTreeSelect();
  pointsQueryListOptions();
  initDeptUserOption();
});

onActivated(() => {
  const isRefresh = route.query.isRefresh;
  if (isRefresh != null) {
    //getList();
  }
});
</script>
