<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="部门名称" prop="deptName">
              <el-input v-model="queryParams.deptName" placeholder="请输入部门名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="类别编码" prop="deptCategory">
              <el-input v-model="queryParams.deptCategory" placeholder="请输入类别编码" clearable style="width: 240px" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="部门状态" clearable >
                <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="hover">
      <template #header>
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button v-hasPermi="['system:dept:add']" type="primary" plain icon="Plus" @click="handleAdd()">新增 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="Sort" @click="handleToggleExpandAll">展开/折叠</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table
        ref="deptTableRef"
        v-loading="loading"
        :data="deptList"
        row-key="deptId"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="isExpandAll"
      >
        <el-table-column prop="deptName" label="部门名称" width="260"></el-table-column>
        <el-table-column prop="orderNum" align="center" label="排序" width="200"></el-table-column>
        <el-table-column prop="openedAccountCount" align="center" label="已开账号积分" width="130"></el-table-column>
        <el-table-column prop="dispatchAccountCount" align="center" label="已分配积分" width="120"></el-table-column>
        <el-table-column prop="accountCount" align="center" label="总积分" width="100"></el-table-column>
        <el-table-column label="区域层级" align="center" prop="areaLevel">
          <template #default="scope">
            <dict-tag v-if="scope.row.areaLevel !== null && scope.row.areaLevel !== undefined" :options="sys_dept_area_level" :value="scope.row.areaLevel"/>
          </template>
        </el-table-column>
        <el-table-column prop="status" align="center" label="状态" width="100">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="200">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" align="center" label="操作" width="200">
          <template #default="scope">
            <el-tooltip v-if="scope.row.parentId !== 0 && !scope.row.ownDepartment" content="分配积分" placement="top">
              <el-button v-hasPermi="['system:dept:auth']" link type="primary" icon="Star" @click="handleAuth(scope.row)" />
            </el-tooltip>
            <el-tooltip v-if="scope.row.areaLevel != 'school'" content="分配记录" placement="top">
              <el-button v-hasPermi="['system:deptDispatchHis:list']" link type="primary" icon="Document" @click="handleAuthHis(scope.row)" />
            </el-tooltip>
            <el-tooltip v-if="scope.row.areaLevel == 'school'" content="分配账号记录" placement="top">
              <el-button v-hasPermi="['system:deptConsumeHis:list']" link type="primary" icon="Document" @click="handleConsumeAuthHis(scope.row)" />
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['system:dept:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)" />
            </el-tooltip>
            <el-tooltip content="新增" placement="top">
              <el-button v-hasPermi="['system:dept:add']" link type="primary" icon="Plus" @click="handleAdd(scope.row)" />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['system:dept:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-dialog v-model="dialog.visible" :title="dialog.title" destroy-on-close append-to-body width="600px">
      <el-form ref="deptFormRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col v-if="form.parentId !== 0" :span="24">
            <el-form-item label="上级部门" prop="parentId">
              <el-tree-select
                v-model="form.parentId"
                :data="deptOptions"
                :props="{ value: 'deptId', label: 'deptName', children: 'children' }"
                value-key="deptId"
                placeholder="选择上级部门"
                check-strictly
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门名称" prop="deptName">
              <el-input v-model="form.deptName" placeholder="请输入部门名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类别编码" prop="deptCategory">
              <el-input v-model="form.deptCategory" placeholder="请输入类别编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="orderNum">
              <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="leader">
              <el-select v-model="form.leader" placeholder="请选择负责人">
                <el-option v-for="item in deptUserList" :key="item.userId" :label="item.userName" :value="item.userId" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门状态">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="区域层级">
              <el-select v-model="form.areaLevel" placeholder="请选择">
                <el-option v-for="dict in sys_dept_area_level" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>


    <el-dialog v-model="authDialog.visible" :title="authDialog.title" destroy-on-close append-to-body width="600px">
      <el-form ref="deptFormRefAuth" :model="formAuth" :rules="rulesAuth" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="我的部门" prop="ownDeptName">
              <el-input v-model="formAuth.ownDeptName"  disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="我的剩余积分数" prop="availableCount">
              <el-input v-if="formAuth.authType=='year'" v-model="formAuth.availableCount"  disabled/>
              <el-input v-if="formAuth.authType=='quarter'" v-model="formAuth.quarterAvailableCount"  disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="分配单位" prop="targetDeptName">
              <el-input v-model="formAuth.targetDeptName"  disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="分配积分数量" prop="changeCount">
              <el-input v-model="formAuth.changeCount" placeholder="请输入分配积分数量"  maxlength="5" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :disabled="disableBtn"  type="primary" @click="submitFormAuth">确 定</el-button>
          <el-button @click="cancelAuth">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="authDialogHis.visible" :title="authDialogHis.title" destroy-on-close append-to-body width="1000px">
      <DeptDispatchHis :param="deptAuthId" />
    </el-dialog>
    
    <el-dialog v-model="authConsumeDialogHis.visible" :title="authConsumeDialogHis.title" destroy-on-close append-to-body width="1000px">
      <DeptConsumeHis :param="deptAuthId" />
    </el-dialog>
  </div>
</template>

<script setup name="Dept" lang="ts">
import { deptAuth,getMyDept,listDept, getDept, delDept, addDept, updateDept, listDeptExcludeChild } from '@/api/system/dept';
import { DeptForm, DeptQuery, DeptVO,AuthDeptForm } from '@/api/system/dept/types';
import { UserVO } from '@/api/system/user/types';
import { listUserByDeptId } from '@/api/system/user';
import DeptDispatchHis from '@/views/system/deptDispatchHis/deptDispatchHis.vue';
import DeptConsumeHis from '@/views/system/deptConsumeHis/index.vue';


interface DeptOptionsType {
  deptId: number | string;
  deptName: string;
  children: DeptOptionsType[];
}

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_normal_disable ,sys_dept_area_level} = toRefs<any>(proxy?.useDict('sys_normal_disable','sys_dept_area_level'));

const deptList = ref<DeptVO[]>([]);
const loading = ref(true);
const disableBtn = ref(false);
const showSearch = ref(true);
const deptOptions = ref<DeptOptionsType[]>([]);
const isExpandAll = ref(true);
const deptUserList = ref<UserVO[]>([]);
const deptAuthId = ref<string|number>('');

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const authDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
const authDialogHis = reactive<DialogOption>({
  visible: false,
  title: ''
});
const authConsumeDialogHis = reactive<DialogOption>({
  visible: false,
  title: ''
});
const deptTableRef = ref<ElTableInstance>();
const queryFormRef = ref<ElFormInstance>();
const deptFormRef = ref<ElFormInstance>();
const deptFormRefAuth = ref<ElFormInstance>();
// 手机号校验规则
const validateAuthCount = (rule, value, callback) => {
  if(formAuth.value.changeCount<1){
    callback(new Error('分配数量必须大于0'));
    return;
  }
  if (value !== undefined && formAuth.value.authType==='year' && formAuth.value.changeCount > formAuth.value.availableCount) {
    callback(new Error('分配数量必须小于等于剩余数量'));
  }else if (value !== undefined && formAuth.value.authType==='quarter' && formAuth.value.changeCount > formAuth.value.quarterAvailableCount) {
    callback(new Error('分配数量必须小于等于剩余数量'));
  }  else {
    callback();
  }
};


const initFormData: DeptForm = {
  deptId: undefined,
  parentId: undefined,
  deptName: undefined,
  deptCategory: undefined,
  orderNum: 0,
  leader: undefined,
  phone: undefined,
  email: undefined,
  status: '0'
};
const initFormDataAuth: AuthDeptForm = {

}
const initData: PageData<DeptForm, DeptQuery> = {
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    deptName: undefined,
    deptCategory: undefined,
    status: undefined
  },
  rules: {
    parentId: [{ required: true, message: '上级部门不能为空', trigger: 'blur' }],
    deptName: [{ required: true, message: '部门名称不能为空', trigger: 'blur' }],
    orderNum: [{ required: true, message: '显示排序不能为空', trigger: 'blur' }],
    email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
    phone: [{ pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'blur' }]
  }
};
const data = reactive<PageData<DeptForm, DeptQuery>>(initData);

const { queryParams, form, rules} = toRefs<PageData<DeptForm, DeptQuery>>(data);

const formAuth = ref<AuthDeptForm>({ ...initFormDataAuth });
const rulesAuth = ref({ 
  changeCount: [
      { required: true, message: '分积分数量不能为空', trigger: 'blur' },
      { pattern: /^[1-9]\d*$/, message: '必须为正整数', trigger: 'blur' },
      { validator: validateAuthCount, trigger: 'blur' }
    ]
});

/** 查询菜单列表 */
const getList = async () => {
  loading.value = true;
  const res = await listDept(queryParams.value);
  const data = proxy?.handleTree<DeptVO>(res.data, 'deptId');
  if (data) {
    deptList.value = data;
  }
  loading.value = false;
};

/** 查询当前部门的所有用户 */
async function getDeptAllUser(deptId: any) {
  if (deptId !== null && deptId !== '' && deptId !== undefined) {
    const res = await listUserByDeptId(deptId);
    deptUserList.value = res.data;
  }
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};
/** 取消按钮 */
const cancelAuth = () => {
  resetAuth();
  authDialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  deptFormRef.value?.resetFields();
};
/** 表单重置 */
const resetAuth = () => {
  formAuth.value = {};
  deptFormRefAuth.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 展开/折叠操作 */
const handleToggleExpandAll = () => {
  isExpandAll.value = !isExpandAll.value;
  toggleExpandAll(deptList.value, isExpandAll.value);
};
/** 展开/折叠所有 */
const toggleExpandAll = (data: DeptVO[], status: boolean) => {
  data.forEach((item) => {
    deptTableRef.value?.toggleRowExpansion(item, status);
    if (item.children && item.children.length > 0) toggleExpandAll(item.children, status);
  });
};

/** 新增按钮操作 */
const handleAdd = async (row?: DeptVO) => {
  reset();
  const res = await listDept();
  const data = proxy?.handleTree<DeptOptionsType>(res.data, 'deptId');
  if (data) {
    deptOptions.value = data;
    if (row && row.deptId) {
      form.value.parentId = row?.deptId;
    }
    dialog.visible = true;
    dialog.title = '添加部门';
  }
};
/** 分配授权账号操作 */
const handleAuth = async (row?: DeptVO) => {
    const resMyDept = await getMyDept();
    formAuth.value.changeCount = null;
    formAuth.value.availableCount = resMyDept.data.availableCount;
    formAuth.value.ownDeptName = resMyDept.data.deptName;
    formAuth.value.ownDeptId = resMyDept.data.deptId;
    formAuth.value.targetDeptName = row.deptName;
    formAuth.value.targetDeptId = row.deptId;
    formAuth.value.quarterAvailableCount = resMyDept.data.quarterAvailableCount;
    formAuth.value.authType = "year";
    authDialog.visible = true;
    authDialog.title = '分配积分';
};
const handleAuthQuarter = async (row?: DeptVO) => {
    const resMyDept = await getMyDept();
    formAuth.value.changeCount = null;
    formAuth.value.availableCount = resMyDept.data.availableCount;
    formAuth.value.ownDeptName = resMyDept.data.deptName;
    formAuth.value.ownDeptId = resMyDept.data.deptId;
    formAuth.value.targetDeptName = row.deptName;
    formAuth.value.targetDeptId = row.deptId;
    formAuth.value.quarterAvailableCount = resMyDept.data.quarterAvailableCount;
    formAuth.value.authType = "quarter";
    authDialog.visible = true;
    authDialog.title = '分配授权账号（季度）';
};

const handleAuthHis = async (row?: DeptVO) => {
    deptAuthId.value = row.deptId;
    authDialogHis.visible = true;
    authDialogHis.title = '积分授权记录';
};

const handleConsumeAuthHis = async (row?: DeptVO) => {
    deptAuthId.value = row.deptId;
    authConsumeDialogHis.visible = true;
    authConsumeDialogHis.title = '积分消耗记录';
};

/** 修改按钮操作 */
const handleUpdate = async (row: DeptVO) => {
  reset();
  //查询当前部门所有用户
  getDeptAllUser(row.deptId);
  const res = await getDept(row.deptId);
  form.value = res.data;
  const response = await listDeptExcludeChild(row.deptId);
  const data = proxy?.handleTree<DeptOptionsType>(response.data, 'deptId');
  if (data) {
    deptOptions.value = data;
    if (data.length === 0) {
      const noResultsOptions: DeptOptionsType = {
        deptId: res.data.parentId,
        deptName: res.data.parentName,
        children: []
      };
      deptOptions.value.push(noResultsOptions);
    }
  }
  dialog.visible = true;
  dialog.title = '修改部门';
};
/** 提交按钮 */
const submitForm = () => {
  deptFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      form.value.deptId ? await updateDept(form.value) : await addDept(form.value);
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};
const submitFormAuth = () => {
  deptFormRefAuth.value?.validate(async (valid: boolean) => {
    if (valid) {
      disableBtn.value = true;
      await deptAuth(formAuth.value);
      authDialog.visible = false;
      await getList();
      disableBtn.value = false
      proxy?.$modal.msgSuccess('操作成功');
    }
  });
};
/** 删除按钮操作 */
const handleDelete = async (row: DeptVO) => {
  await proxy?.$modal.confirm('是否确认删除名称为"' + row.deptName + '"的数据项?');
  await delDept(row.deptId);
  await getList();
  proxy?.$modal.msgSuccess('删除成功');
};

onMounted(() => {
  getList();
});
</script>
